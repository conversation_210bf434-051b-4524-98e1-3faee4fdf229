# 首面

# 简述 （5-10 min）

## 介绍下最近半年内的工作内容，用到了哪些技术，平时主要工作任务都做什么（团队构成，自己定位，前后端协作的方式...）

## 介绍一两个其中遇到的难点问题，以及解决思路，或者其间觉得对自己技术或职业认知上有所成长的分享

# 口述 （15 - 20 min）

## typescript (5min)

- ts type vs class vs interface 差异，适用场景， type vs interface
- ts 泛型的目的，用法，什么是协变逆变（自动推导）

## nextjs (5min)

### 1 在 Next.js 中，渲染策略都有哪些，分别适用于哪些场景？如何选择？

- 参考答案：
  - SSR：动态内容（如用户仪表盘），需实时数据且 SEO 敏感。
  - SSG：静态内容（如博客、产品页），构建时生成，CDN 缓存。
  - ISR：增量更新（如电商商品页），允许后台重新生成页面。

### 2 'use server', 'use client' 的差异, 什么时候用什么, layout 在 app router 如何嵌套，嵌套后如何排除

## react (5min)

### 1 React 中如何优化渲染？ Next.js 如何配合？常见的优化策略

- 参考答案：
  - React：使用 `React.memo` 或 `useMemo` 避免重复渲染，虚拟列表库（如 react-window）减少 DOM 节点。
  - Next.js：动态导入组件（`dynamic import`）结合 Suspense 实现代码分割。

### 2 react 中 hook 的 call order 为什么不能被打断, 如果需要条件判断时候，应该如何解决

- ref:
  - move to useeffect
  - wrap into another custome hook

# 机题 (20 min)

## typescript (10min)

### 1 （5min）

在 ts 中定义一个类型，将所有参数类型的属性中，所有可为空（null or undefined）的类型都转化为非空类型

适用如下代码，测试 (https://www.typescriptlang.org/play/)

```typescript
type B = {
    age?: number
    name: string
    addr: string | null
}

type xxxx ....

// 使得 a1 报错， a2 成功, xxxx 为构建的类型
let a1: xxxx = { name: "test", addr: null }
let a2: xxxx = { name: "test", addr: "chengdu" }
```

### 2 (5min)

解释如下代码：

```typescript
type B = {
  age?: number;
  name: string;
  addr: string | undefined;
};

type A<T> = {
  [K in keyof T]: T[K] extends null | undefined ? never : T[K];
};

type C<T> = T extends null | undefined ? never : T;
type A1<T> = {
  [K in keyof T]: C<T[K]>;
};

let b1: B = { name: "hi", addr: undefined };

// 以下会报错吗？ 原因
let a1: A<B> = b1;
let a2: A1<B> = b1;

// ----------------

let b2: B = { name: "hi", addr: "chengdu" };

// 以下会报错吗？ 原因
let a3: A1<B> = b2;
let a4: A1<B> = { name: "hi", addr: "chengdu" };
```

## react (10 min)

### 1 如何用 React Hooks + TypeScript 实现一个支持撤销/重做功能的历史状态管理器？ (5min)

参考答案：  
通过 useReducer+双向链表实现：

```typescript
type HistoryState<T> = {
  past: T[];
  present: T;
  future: T[];
};

const useHistory = <T>(initialState: T) => {
  const [{ past, present, future }, dispatch] = useReducer(
    (
      state: HistoryState<T>,
      action:
        | { type: "UNDO" }
        | { type: "REDO" }
        | { type: "SET"; newPresent: T }
    ) => {
      // 类型安全的状态转换逻辑
    },
    { past: [], present: initialState, future: [] }
  );

  return {
    state: present,
    undo: () => dispatch({ type: "UNDO" }),
    redo: () => dispatch({ type: "REDO" }),
    setState: (newState: T) => dispatch({ type: "SET", newPresent: newState }),
  };
};
```

### 2 如何用 React Suspense + Error Boundaries 实现带有竞态防护的异步数据流？ (5min)

参考答案：  
通过 AbortController+缓存策略：

```typescript
class RaceConditionProtector {
  private controllerMap = new Map<string, AbortController>();

  async fetch<T>(key: string, url: string): Promise<T> {
    this.controllerMap.get(key)?.abort();
    const controller = new AbortController();
    this.controllerMap.set(key, controller);

    const res = await fetch(url, { signal: controller.signal });
    return res.json();
  }
}

// 在组件中配合Suspense和Error Boundary使用
const DataComponent = ({ id }: { id: string }) => {
  const data = useAsyncData(id); // 自定义Hook封装
  return <div>{data.content}</div>;
};

const Wrapper = () => (
  <ErrorBoundary>
    <Suspense fallback={<Loader />}>
      <DataComponent id="123" />
    </Suspense>
  </ErrorBoundary>
);
```

核心机制：

- AbortController 取消旧请求
- Suspense 协调加载状态
- Error Boundary 捕获异常

# demo (30min)

现公司需要紧急上线一个 landing page，设计如下，希望快速发布上线，请尝试根据设计稿在本地写出静态页，同时给出需要快速上线发布的一些思路。

- 设计稿： https://mastergo.com/files/invite-member/141966387911633?inviter=119702268858429
- 动效地址：https://threejs.org/examples/#webgl_buffergeometry_drawrange

基础要求：

- 尽可能还原 （视觉上）
- 尽可能快的实现

加分：

- 基于 react
- 尽可能少的外部库依赖
- ts
- 屏幕大小适配
- 响应式适配

## misc

### 有了 usecontext, usereducer, 还需要 redux 吗，是否完全取代呢

### 如何实现 Next.js 中 API 的鉴权保障？

- 参考答案：
  中间件（Middleware）的鉴权逻辑，jwt
  - 在 `middleware.ts` 中校验 JWT，重定向未授权请求。
  - 示例：
    ````typescript
    export function middleware(req: NextRequest) {
      const token = req.cookies.get("auth");
      if (!token) return NextResponse.redirect("/login");
    }
    ```
    ````

### oauth, oidc 的差异， jwt 在公开的解析下安全性在哪里，如果做 sign 验证，alg 对称，非对称，如果要做加密怎么实现（jwe,jwk）

## 线下作业

从以下题中按自己能力选择

### 1

请针对 https://github.com/hms-dbmi/viv 库，了解其作用和实现原理

基础：

- 梳理实现原理
- 解释通道渲染逻辑
- 通道渲染有没有通道数量上的限制，为什么？如果想要不限制通道数量，该如何实现

加分：

- 做出 demo 实现出不限制通道数量的渲染

### 2

如果公司要快速推进上线一个功能和 https://chat.deepseek.com/ 一样的网站，提供模型对话服务，请自行调研方案以及实现

基础：

- 复刻围绕 AI 对话 交互的 UI
  - siderbar，history 模块, chat 模块
  - 能够实现： ai message stream, message edit&copy, switch thread，rename thread，delete thread
  - 可忽略： 个人信息模块，对话框中附件处理模块
- 如果基于现成开源框架魔改，要保证 UI 交互上没有额外模块，能对标 ds 官网

加分：

- UI 交互的复刻，越完备越好
- 调通对接 LLM 的调用，可直接使用模型对话
- 如果 UI 完成很快，可以思考后端服务该如何适配，数据的持久化，收费模式，模型服务商的切换...

### 3

基于 https://deck.gl/docs/api-reference/layers/bitmap-layer ， 实现一个 图片 在 browser 里的渲染，不需要底层 geo map，就是单纯的图片渲染，并能够对其 缩放，拖动。
然后结合 https://visgl.github.io/deck.gl-community/docs/modules/editable-layers/api-reference/layers/editable-geojson-layer 实现在这个 图片 上，进行 ROI 的绘制，绘制只需要支持 rectangle 就好，且能够对所绘制的 ROI 进行拖动，改变其位置。

- 功能上类似 https://nebula.gl/geojson-editor/ 这里的 Draw Rectangle 的绘制，以及 Alter Modes 下 Translate 对绘制的 rectangle 进行移动。
