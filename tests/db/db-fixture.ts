import { getPrisma } from "@/lib/prisma";
import { afterEach, beforeEach } from "vitest";
import { PrismaTestingHelper } from "@chax-at/transactional-prisma-testing";
import { cacheLazy } from "@/lib/lazy";

const getTestPrismaHelper = cacheLazy(async () => {
  const prisma = await getPrisma();
  const testPrisma = new PrismaTestingHelper(prisma);
  return testPrisma;
});

beforeEach(async () => {
  const testPrisma = await getTestPrismaHelper();
  await testPrisma.startNewTransaction();
});

afterEach(async () => {
  const testPrisma = await getTestPrismaHelper();
  testPrisma.rollbackCurrentTransaction();
});

export const getTestPrisma = async () => {
  const testPrisma = await getTestPrismaHelper();
  return testPrisma.getProxyClient();
};
