import { describe } from "node:test";
import { expect, it } from "vitest";
import { getTestPrisma } from "./db-fixture";
import { getItem, ItemTypeFlag } from "@/lib/domain/item/svc";

describe("getItem", async () => {
  it("should get item", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    const itemFromDb = await getItem(prisma, { itemId: mockItem.id });
    expect(itemFromDb).toEqual(mockItem);
  });

  it("should throw if item deleted", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
        deleted_at: 1,
      },
    });

    const itemFromDbPromise = getItem(prisma, { itemId: mockItem.id });
    await expect(itemFromDbPromise).rejects.toThrow("item_not_found");
  });

  it("should throw if type flag not matched", async () => {
    const prisma = await getTestPrisma();
    const mockItem = await prisma.item.create({
      data: {
        piid_path: "/prjId/xxx/",
        name: "item-name",
        type_flag: ItemTypeFlag.File,
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    const itemFromDbPromise = getItem(prisma, {
      itemId: mockItem.id,
      typeFlag: ItemTypeFlag.Folder,
    });

    await expect(itemFromDbPromise).rejects.toThrow("item_not_found");
  });
});
