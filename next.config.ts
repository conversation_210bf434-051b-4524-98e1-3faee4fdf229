import type { NextConfig } from "next";

type JSONValue =
  | string
  | number
  | boolean
  | JSONValue[]
  | {
      [k: string]: JSONValue;
    };

const svgrLoaderOptions = {
  loader: "@svgr/webpack",
  options: {
    icon: true,
    svgoConfig: {
      // https://react-svgr.com/docs/options/#svgo-config
      plugins: [
        {
          name: "preset-default",
          params: {
            overrides: {
              removeViewBox: false,
              removeUnknownsAndDefaults: false,
            },
          },
        },
        "removeComments",
        "removeMetadata",
        "removeDoctype",
        "removeXMLProcInst",
        "removeEmptyText",
        "cleanupNumericValues",
        {
          name: "convertColors",
          params: { currentColor: true },
        },
        {
          name: "removeAttrs",
          params: { attrs: ["fill-opacity"] },
        },
      ],
    },
  },
};

const nextConfig: NextConfig = {
  /* config options here */
  output: "standalone",
  async rewrites() {
    return [{ source: "/", destination: "/landing" }];
  },

  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error"],
          }
        : false,
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: [svgrLoaderOptions],
    });

    return config;
  },
  turbopack: {
    rules: {
      "*.svg": {
        loaders: [
          {
            loader: "@svgr/webpack",
            options: svgrLoaderOptions.options as Record<string, JSONValue>,
          },
        ],
        as: "*.js",
      },
    },
  },
};

export default nextConfig;
