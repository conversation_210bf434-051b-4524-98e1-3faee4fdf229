-- CreateTable
CREATE TABLE "item" (
    "iid" SERIAL NOT NULL,
    "id" TEXT NOT NULL,
    "piid_path" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type_flag" INTEGER NOT NULL DEFAULT 0,
    "size" DOUBLE PRECISION,
    "task_id" TEXT,
    "created_by_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by_id" TEXT NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" BIGINT NOT NULL DEFAULT 0,

    CONSTRAINT "item_pkey" PRIMARY KEY ("iid")
);

-- CreateTable
CREATE TABLE "task" (
    "id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "task_type" TEXT NOT NULL,
    "task_args" JSONB NOT NULL,
    "remote_task_id" TEXT,
    "remote_task_status" TEXT,
    "start_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "finished_at" TIMESTAMP(3),
    "error" TEXT,
    "created_by_id" TEXT NOT NULL,
    "extra" JSONB,

    CONSTRAINT "task_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "item_id_key" ON "item"("id");

-- CreateIndex
CREATE INDEX "item_deleted_at_idx" ON "item"("deleted_at");

-- CreateIndex
CREATE UNIQUE INDEX "item_piid_path_name_deleted_at_key" ON "item"("piid_path", "name", "deleted_at");

-- CreateIndex
CREATE INDEX "task_project_id_idx" ON "task"("project_id");

-- CreateIndex
CREATE INDEX "task_remote_task_id_idx" ON "task"("remote_task_id");

-- AddForeignKey
ALTER TABLE "item" ADD CONSTRAINT "item_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "task"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task" ADD CONSTRAINT "task_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
