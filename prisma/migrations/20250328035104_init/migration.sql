-- CreateTable
CREATE TABLE "token_consumption" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "tokens" DECIMAL(65,30) NOT NULL,
    "consumed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_preconsumption" BOOLEAN NOT NULL,
    "extra_info" JSONB NOT NULL,

    CONSTRAINT "token_consumption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "recharge" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "status" TEXT NOT NULL,
    "atp" DECIMAL(65,30) NOT NULL,
    "pay_transaction_id" TEXT,
    "pay_time" TIMESTAMP(3),
    "pay_callback_time" TIMESTAMP(3),
    "pay_callback_result" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "recharge_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "transaction" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "atp" DECIMAL(65,30) NOT NULL,
    "source_type" TEXT NOT NULL,
    "source_info" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "failed_recharge" (
    "id" TEXT NOT NULL,
    "recharge_id" TEXT NOT NULL,
    "reason" TEXT NOT NULL,

    CONSTRAINT "failed_recharge_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_balance" (
    "user_id" TEXT NOT NULL,
    "atp" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "version" TEXT NOT NULL DEFAULT '0',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_balance_pkey" PRIMARY KEY ("user_id")
);

-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "phone" TEXT NOT NULL,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "account" (
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "account_pkey" PRIMARY KEY ("provider","providerAccountId")
);

-- CreateTable
CREATE TABLE "thread" (
    "id" TEXT NOT NULL,
    "title" TEXT,
    "last_message_at" TIMESTAMP(3) NOT NULL,
    "metadata" JSONB,
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "is_archived" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "thread_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "message" (
    "id" TEXT NOT NULL,
    "thread_id" TEXT NOT NULL,
    "parent_id" TEXT,
    "format" TEXT NOT NULL DEFAULT 'aui/v0',
    "content" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "message_pkey" PRIMARY KEY ("thread_id","id")
);

-- CreateIndex
CREATE INDEX "token_consumption_user_id_consumed_at_idx" ON "token_consumption"("user_id", "consumed_at");

-- CreateIndex
CREATE INDEX "token_consumption_is_preconsumption_idx" ON "token_consumption"("is_preconsumption");

-- CreateIndex
CREATE INDEX "recharge_user_id_created_at_idx" ON "recharge"("user_id", "created_at");

-- CreateIndex
CREATE INDEX "recharge_pay_transaction_id_idx" ON "recharge"("pay_transaction_id");

-- CreateIndex
CREATE INDEX "transaction_user_id_created_at_idx" ON "transaction"("user_id", "created_at");

-- CreateIndex
CREATE UNIQUE INDEX "user_phone_key" ON "user"("phone");

-- CreateIndex
CREATE INDEX "thread_user_id_idx" ON "thread"("user_id");

-- CreateIndex
CREATE INDEX "message_thread_id_idx" ON "message"("thread_id");

-- AddForeignKey
ALTER TABLE "user_balance" ADD CONSTRAINT "user_balance_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "account" ADD CONSTRAINT "account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_thread_id_fkey" FOREIGN KEY ("thread_id") REFERENCES "thread"("id") ON DELETE CASCADE ON UPDATE CASCADE;
