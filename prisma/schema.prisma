// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_DIRECT_URL")
}

model TokenConsumption {
  id                String   @id @default(cuid())
  user_id           String
  atp               Decimal  @default(0)
  tokens            Decimal
  consumed_at       DateTime @default(now())
  is_preconsumption Boolean
  extra_info        Json

  @@index([user_id, consumed_at])
  @@index([is_preconsumption])
  @@map("token_consumption")
}

model Recharge {
  id      String  @id @default(cuid())
  user_id String
  amount  Decimal
  status  String
  atp     Decimal

  pay_transaction_id  String?
  pay_time            DateTime?
  pay_callback_time   DateTime?
  pay_callback_result Json?

  created_at DateTime @default(now())

  @@index([user_id, created_at])
  @@index([pay_transaction_id])
  @@map("recharge")
}

model Transaction {
  id      String  @id @default(cuid())
  user_id String
  atp     Decimal

  source_type String
  source_info Json

  created_at DateTime @default(now())

  @@index([user_id, created_at])
  @@map("transaction")
}

model FailedRecharge {
  id          String @id @default(cuid())
  recharge_id String
  reason      String

  @@map("failed_recharge")
}

model UserBalance {
  user_id String @id

  atp     Decimal @default(0)
  version String  @default("0")

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("user_balance")
}

model User {
  id                    String    @id @default(cuid())
  name                  String?
  phone                 String    @unique
  email                 String?
  emailVerified         DateTime?
  image                 String?
  accounts              Account[]
  id_card_verified      DateTime?
  id_card_info          String?
  redacted_id_card_no   String?
  redacted_id_card_name String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserBalance UserBalance?

  @@map("user")
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("account")
}

model Thread {
  id              String    @id @default(cuid())
  title           String?
  last_message_at DateTime
  metadata        Json?
  user_id         String
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  is_archived     Boolean   @default(false)
  messages        Message[]
  model_config    Json?

  @@index([user_id])
  @@map("thread")
}

model Message {
  id        String  @default(cuid())
  thread_id String
  parent_id String?
  format    String  @default("aui/v0")
  content   Json

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  thread     Thread   @relation(fields: [thread_id], references: [id], onDelete: Cascade)

  @@id([thread_id, id])
  @@index([thread_id])
  @@map("message")
}

model Project {
  id String @id @default(cuid())

  name   String
  remark String?

  created_at    DateTime @default(now())
  created_by_id String

  updated_at    DateTime @updatedAt
  updated_by_id String

  deleted_at Float @default(0)

  tasks Task[]

  @@map("project")
}

model Item {
  // int iid here is for generate shorter piid_path
  iid       Int     @id @default(autoincrement())
  id        String  @unique @default(cuid())
  // e.g. /proj_id/1/2/3/4/5/6/ , start with / and piid, separate with /, 
  // also must end with /
  piid_path String
  name      String
  // we also use this flag to express one that is partially generated
  type_flag Int     @default(0)
  size      Float? // only file have size
  task_id   String?

  created_by_id String
  created_at    DateTime @default(now())
  updated_by_id String
  updated_at    DateTime @updatedAt
  deleted_at    Float    @default(0) // 0 means non deleted

  task Task? @relation(fields: [task_id], references: [id], onDelete: SetNull)

  @@unique([piid_path, name, deleted_at])
  @@index([deleted_at])
  @@map("item")
}

model Task {
  id String @id @default(cuid())

  project_id         String
  task_type          String
  task_args          Json
  remote_task_id     String?
  remote_task_status String?
  start_at           DateTime  @default(now())
  finished_at        DateTime?
  error              String?
  created_by_id      String
  extra              Json?

  items   Item[]
  project Project @relation(fields: [project_id], references: [id], onDelete: Restrict)

  @@index([project_id])
  @@index([remote_task_id])
  @@map("task")
}
