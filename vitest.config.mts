import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { defineConfig } from "vitest/config";

import nextjsEnv from "@next/env";
nextjsEnv.loadEnvConfig(process.cwd());

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: "jsdom",
    setupFiles: "vitest-setup.ts",
    server: {
      deps: {
        inline: ["next-auth"],
      },
    },
    projects: [
      {
        extends: true,
        test: {
          name: "unit-test",
          includeSource: ["src/**/*.ts"],
          include: [],
        },
      },
      {
        extends: true,
        test: {
          name: "db-test",
          include: ["tests/db/*.test.ts"],
        },
      },
    ],
    coverage: {
      thresholds: {
        branches: 70,
        functions: 28.37,
        lines: 20.25,
        statements: 20.25,
        autoUpdate: true,
      },
    },
  },
  define: {
    "import.meta.vitest": "undefined", // might not be needed as we are using nextjs
  },
});