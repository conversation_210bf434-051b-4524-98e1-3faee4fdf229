{"name": "diverse", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint && tsc", "test": "vitest", "test:coverage": "vitest --coverage", "ut": "vitest --project unit-test", "dbt": "vitest --project db-test", "postinstall": "prisma generate", "route-path": "pnpx nextjs-paths generate -f route-path.ts"}, "dependencies": {"@47ng/cloak": "^1.2.0", "@ai-sdk/openai-compatible": "^0.2.11", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/react": "^1.2.12", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@assistant-ui/react": "^0.10.19", "@assistant-ui/react-ai-sdk": "^0.10.13", "@assistant-ui/react-edge": "^0.2.11", "@assistant-ui/react-markdown": "^0.10.4", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "3.717.0", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@restatedev/restate-sdk": "^1.8.1", "@restatedev/restate-sdk-clients": "^1.8.1", "@svgr/webpack": "^8.1.0", "@tus/s3-store": "1.9.0", "@tus/server": "^2.1.0", "@uppy/core": "^4.4.7", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.4.0", "@uppy/tus": "^4.2.2", "@zumer/snapdom": "^1.2.2", "ai": "^4.3.16", "ali-oss": "^6.23.0", "aliyun-api-gateway": "^1.1.6", "antd": "^5.25.4", "authing-node-sdk": "^4.0.1", "big.js": "^6.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "es-toolkit": "^1.38.0", "lucide-react": "^0.484.0", "mermaid": "^11.6.0", "next": "15.3.3", "next-auth": "5.0.0-beta.29", "next-navigation-guard": "^0.2.0", "next-safe-action": "^8.0.8", "node-forge": "^1.3.1", "nuqs": "^2.4.3", "p-retry": "7.0.0-0", "proxy-agent": "^6.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-shiki": "^0.6.0", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.4.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "wechatpay-nextjs-v3": "^1.0.1", "wretch": "^2.11.0", "zod": "^3.25.42", "zod-config": "^0.1.4"}, "devDependencies": {"@chax-at/transactional-prisma-testing": "^1.3.0", "@eslint/eslintrc": "^3.3.1", "@next/env": "^15.3.3", "@next/eslint-plugin-next": "^15.3.5", "@restatedev/restate-sdk-zod": "^1.7.3", "@tailwindcss/postcss": "^4.1.8", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/ali-oss": "^6.16.11", "@types/big.js": "^6.2.2", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/three": "^0.174.0", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "3.2.4", "eslint": "^9.28.0", "eslint-config-next": "15.2.4", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "postcss": "^8.5.4", "prisma": "^6.13.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "3.2.4", "vitest-mock-extended": "^3.1.0"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}