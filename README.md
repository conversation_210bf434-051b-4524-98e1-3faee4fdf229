# Diverse

[![codecov](https://codecov.io/github/luminiris/diverse/graph/badge.svg?token=CK6814ELRM)](https://codecov.io/github/luminiris/diverse)

# dev note:

## mise task

- `mise task ls` to see all tasks
- `mise r <task-name>` to run a task

## env

- 统一了一下 env 的使用方式， 本地开发需要重写的用 .env.local 覆盖就好，目前基本上就只需要自己添加一个 OPENAI_API_KEY 就行，其他配置走统一的
- .env 文件是所有配置的基准，默认也是生产环境的配置。 里面只能写非敏感信息，敏感信息留空（部署的时候会处理）
- .env.development 文件是开发环境共同分享的，需要覆盖生产环境的配置，里面可以直接填敏感信息，只要敏感信息无害（比如我们自己建立的一个假的 authing 测试账户），方便开发统一环境用一个
- .env.local 放开发环境过程中需要用到的，不能写到 .env.development 的敏感信息， 比如我们每个人自己的 openai-api-key 。 或者一些场景，自己开发过程中想要重写适配的。

## wechat-pay

- mock 了开发过程中需要用到的微信支付，支付页面刷新出二维码，基本上等 30s，就会当做充值成功。
