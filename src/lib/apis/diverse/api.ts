import { diskEndpoints } from "@/lib/apis/diverse/disk-endpoints";
import { itemEndpoints } from "@/lib/apis/diverse/item-endpoints";
import { llmEndpoints } from "@/lib/apis/diverse/llm-endpoints";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// Define a service using a base URL and expected endpoints
export const baseApi = createApi({
  reducerPath: "diverseApi",
  baseQuery: fetchBaseQuery({ baseUrl: "/api" }),
  tagTypes: ["thread", "disk", "item"],
  endpoints: () => ({}),
});

export type DiverseApiEndpointBuilder = Parameters<
  Parameters<typeof baseApi.injectEndpoints>[0]["endpoints"]
>[0];

export const diverseApi = baseApi
  .injectEndpoints({
    endpoints: llmEndpoints,
  })
  .injectEndpoints({
    endpoints: diskEndpoints,
  })
  .injectEndpoints({
    endpoints: itemEndpoints,
  });
