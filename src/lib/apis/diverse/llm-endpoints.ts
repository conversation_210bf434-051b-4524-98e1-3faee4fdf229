import { GetThreadRes } from "@/app/api/v1/threads/[thread_id]/route";
import type { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";

export const llmEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    getThread: builder.query<GetThreadRes, string>({
      query: (thread_id) => {
        return {
          url: `v1/threads/${thread_id}`,
        };
      },
      providesTags(_, err, req) {
        return err ? [] : [{ type: "thread", id: req }];
      },
    }),
  };
};
