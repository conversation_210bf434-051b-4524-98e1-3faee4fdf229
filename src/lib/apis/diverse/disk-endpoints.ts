import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";
import { type ListDiskRes } from "@/app/api/disk/route";
import { type ListObjectsInput } from "@/lib/domain/disk/svc";

export const diskEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    listDisk: builder.query<ListDiskRes, ListObjectsInput>({
      query: (req) => {
        return {
          url: "disk",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err ? [] : [{ type: "disk", id: generateDiskTagId(req) }];
      },
    }),
  };
};

export function generateDiskTagId({
  projectId,
  prefix,
}: {
  projectId: string;
  prefix: string;
}) {
  return `${projectId}/${prefix}`;
}
