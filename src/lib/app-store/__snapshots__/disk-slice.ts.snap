// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`buildTreeData > should build tree data 1`] = `
{
  "children": [
    {
      "isLeaf": true,
      "key": "/file1",
      "size": 100,
      "title": "file1",
    },
    {
      "isLeaf": true,
      "key": "/file2",
      "size": 100,
      "title": "file2",
    },
    {
      "children": [
        {
          "children": [],
          "isLeaf": false,
          "key": "/dir1/sd1/",
          "title": "sd1",
        },
        {
          "isLeaf": true,
          "key": "/dir1/sd2",
          "size": undefined,
          "title": "sd2",
        },
        {
          "isLeaf": true,
          "key": "/dir1/sd3.txt",
          "size": 100,
          "title": "sd3.txt",
        },
      ],
      "isLeaf": false,
      "key": "/dir1/",
      "title": "dir1",
    },
    {
      "children": [],
      "isLeaf": false,
      "key": "/dir2/",
      "title": "dir2",
    },
  ],
  "isLeaf": false,
  "key": "/",
  "title": "云盘",
}
`;
