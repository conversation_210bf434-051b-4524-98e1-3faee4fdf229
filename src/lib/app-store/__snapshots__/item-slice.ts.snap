// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`buildTreeData > should build tree data correctly 1`] = `
{
  "children": [
    {
      "children": [],
      "isLeaf": true,
      "key": "aghah3",
      "name": "肺癌9表10色",
      "namePath": "/肺癌9表10色",
      "pid": "prjId",
      "pids": [
        "prjId",
      ],
      "size": 325,
      "title": "肺癌9表10色",
    },
    {
      "children": undefined,
      "isLeaf": false,
      "key": "aghah1",
      "name": "数据组",
      "namePath": "/数据组",
      "pid": "prjId",
      "pids": [
        "prjId",
      ],
      "size": undefined,
      "title": "数据组",
    },
    {
      "children": [
        {
          "children": [],
          "isLeaf": false,
          "isNewFolder": true,
          "key": "new-folder",
          "name": "",
          "pid": "unnni",
          "pids": [
            "prjId",
            "unnni",
          ],
          "title": "新数据源",
        },
        {
          "children": undefined,
          "isLeaf": false,
          "key": "aghah5",
          "name": "神经元1",
          "namePath": "/小鼠脑神经元St38901/神经元1",
          "pid": "unnni",
          "pids": [
            "prjId",
            "unnni",
          ],
          "size": undefined,
          "title": "神经元1",
        },
        {
          "children": undefined,
          "isLeaf": false,
          "key": "aghah6",
          "name": "神经元2",
          "namePath": "/小鼠脑神经元St38901/神经元2",
          "pid": "unnni",
          "pids": [
            "prjId",
            "unnni",
          ],
          "size": undefined,
          "title": "神经元2",
        },
      ],
      "isLeaf": false,
      "key": "unnni",
      "name": "小鼠脑神经元St38901",
      "namePath": "/小鼠脑神经元St38901",
      "pid": "prjId",
      "pids": [
        "prjId",
      ],
      "size": undefined,
      "title": "小鼠脑神经元St38901",
    },
  ],
  "isLeaf": false,
  "key": "prjId",
  "name": "数据",
  "title": "数据",
}
`;
