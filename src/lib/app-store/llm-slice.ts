import { IrsModelId, ModelInfo, Models } from "@/lib/domain/ai/llm/models";
import type { PayloadAction } from "@reduxjs/toolkit";
import assert from "assert";
import { saveThreadConfig } from "@/app-ui/actions/safe-actions";
import { createAppSlice, getAppState } from "@/lib/app-store/async-thunk";
import { cloneDeep } from "es-toolkit";
import { diverseApi } from "@/lib/apis/diverse/api";

type ModelConfig = ModelInfo & {};

interface State {
  modelCfg: ModelConfig;
}

function getModelDefaultConfig(modelId: IrsModelId): ModelConfig {
  const modelDefaultCfg = Models.find((x) => x.id == modelId);
  assert(modelDefaultCfg, `${modelId} not found`);
  return cloneDeep(modelDefaultCfg);
}

const initialState: State = {
  modelCfg: getModelDefaultConfig(IrsModelId.DeepSeekR1),
};

export const llmSlice = createAppSlice({
  name: "llm",
  initialState: initialState,
  reducers: (create) => {
    return {
      setModelCfg: create.reducer(
        (
          state,
          {
            payload: { modelId, temperature, maxTokens },
          }: PayloadAction<{
            modelId: IrsModelId;
            temperature?: number;
            maxTokens?: number;
          }>
        ) => {
          const dftCfg = getModelDefaultConfig(modelId);

          if (temperature !== undefined) {
            dftCfg.temperature.value = temperature;
          }

          if (maxTokens !== undefined) {
            dftCfg.maxTokens.value = maxTokens;
          }

          state.modelCfg = dftCfg;
        }
      ),
      resetModelCfg: create.reducer((state) => {
        state.modelCfg = getModelDefaultConfig(state.modelCfg.id);
      }),
      updateTemperature: create.reducer((s, action: PayloadAction<number>) => {
        s.modelCfg.temperature.value = action.payload;
      }),
      updateMaxTokens: create.reducer((s, action: PayloadAction<number>) => {
        s.modelCfg.maxTokens.value = action.payload;
      }),
      saveCfgToRemote: create.asyncThunk(async (threadId: string, api) => {
        const cfg = llmSlice.selectors.selectModelCfg(getAppState(api));
        // TODO: handle error for next-safe-action res
        const res = await saveThreadConfig({
          threadId,
          config: {
            modelId: cfg.id,
            maxTokens: cfg.maxTokens.value,
            temperature: cfg.temperature.value,
          },
        });

        api.dispatch(
          diverseApi.util.invalidateTags([{ type: "thread", id: threadId }])
        );
        return res;
      }),
    };
  },
  selectors: {
    selectModelId: (s) => s.modelCfg.id,
    selectModelCfg: (s) => s.modelCfg,
    selectModelParamsChanged: (s) => {
      const dftCfg = getModelDefaultConfig(s.modelCfg.id);
      const changed =
        dftCfg.maxTokens.value != s.modelCfg.maxTokens.value ||
        dftCfg.temperature.value != s.modelCfg.temperature.value;

      return changed;
    },
  },
});
