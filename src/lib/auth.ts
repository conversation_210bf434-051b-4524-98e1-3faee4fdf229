import { PrismaAdapter } from "@auth/prisma-adapter";
import NextA<PERSON> from "next-auth";
// import WeChat from "next-auth/providers/wechat";
import { type AppConfig } from "@/lib/app-config";
import type { SessionUser } from "@/lib/domain/user/m";
import { type AuthingClient } from "@/lib/infra/authing/client";
import type { PrismaClient } from "@prisma/client";
import { KickUsersOptionsDto } from "authing-node-sdk/dist/models/KickUsersOptionsDto";

export const authing_provider_id = "authing";

declare module "next-auth" {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: SessionUser;
  }

  interface User {
    phone: string;
  }
}

declare module "@auth/core/adapters" {
  interface AdapterUser {
    id: string;
    phone: string;
    name: string;
  }
}

export async function configNextAuth(
  appConfig: AppConfig,
  primsa: PrismaClient,
  authing: AuthingClient
) {
  const adapter = PrismaAdapter(primsa);
  const nextAuth = NextAuth({
    debug: appConfig.AUTH_DEBUG,
    adapter: adapter,
    session: { strategy: "jwt" },
    providers: [
      // WeChat({
      //   platformType: "WebsiteApp",
      // }),
      {
        id: authing_provider_id, // signIn("my-provider") and will be part of the callback URL
        name: "By Authing", // optional, used on the default login page as the button text.
        type: "oidc", // or "oauth" for OAuth 2 providers
        issuer: `${appConfig.AUTHING_APP_HOST}/oidc`, // to infer the .well-known/openid-configuration URL
        clientId: appConfig.AUTHING_ID, // from the provider's dashboard
        clientSecret: appConfig.AUTHING_SECRET, // from the provider's dashboard
        authorization: {
          params: {
            scope: "openid username phone",
          },
        },
        profile(profile) {
          return {
            id: profile.sub,
            name: profile.username,
            phone: profile.phone_number,
          };
        },
      },
    ],
    callbacks: {
      async session({ session, token }) {
        // return session is what exposed to frontend when used by hooks,
        // token here is what came from the jwt callback
        session.user.id = token.sub!;
        session.user.redacted_phone = token.redacted_phone as string;
        return session;
      },
      async jwt({ token, user }) {
        if (user) {
          token.redacted_phone =
            user.phone.slice(0, 3) + "****" + user.phone.slice(7);
        }
        // decide what to put in the JWT token that is saved in session cookie
        return token;
      },
    },
    events: {
      signOut: async (msg) => {
        const { sub } = (msg as { token: Record<string, string> }).token;
        const account = await primsa.account.findFirst({
          where: { userId: sub },
          include: { user: { select: { phone: true } } },
        });

        if (account && account.provider == authing_provider_id) {
          await authing.mclient.kickUsers({
            appIds: [appConfig.AUTHING_ID],
            userId: account.user.phone,
            options: { userIdType: KickUsersOptionsDto.userIdType.PHONE },
          });
        }
      },
    },
  });
  return nextAuth;
}
