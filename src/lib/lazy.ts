export function cacheLazy<T>(initializer: () => Promise<T>): {
  (): Promise<PERSON>ike<T>;
  reload(): PromiseLike<T>;
} {
  let value: T | undefined;
  let initialized = false;

  const lazyAsync = async () => {
    if (!initialized) {
      value = await initializer();
      initialized = true;
    }

    return value!;
  };

  lazyAsync.reload = async () => {
    value = await initializer();
    return value;
  };

  return lazyAsync;
}

export function cacheLazySync<T>(initializer: () => T): {
  (): T;
  reload(): T;
} {
  let value: T | undefined;
  let initialized = false;

  const lazySync = () => {
    if (!initialized) {
      value = initializer();
      initialized = true;
    }

    return value!;
  };

  lazySync.reload = () => {
    value = initializer();
    return value;
  };

  return lazySync;
}
