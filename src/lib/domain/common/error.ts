export type DomainErrorCode =
  | "auth_failed"
  | "no_permission"
  | "thread_not_found"
  | "payclient_error"
  | "atp_not_sufficient"
  | "balance_version_changed"
  | "recharge_order_status_changed"
  | "arg_parse_failed"
  | "llm_not_found"
  | "id_verify_error"
  | "no_available_provider"
  | "username_already_exists"
  | "item_not_found"
  | "task_type_not_supported"
  | "start_task_api_failed"
  | "forbid_to_move_to_sub_dir"
  | "project_not_found"
  | "item_name_conflict"
  | "disk_object_already_exists";

export class DomainError extends Error {
  constructor(
    public readonly error_code: DomainErrorCode,
    public readonly extra?: unknown
  ) {
    super(error_code);
    this.name = "DomainError";
  }

  toString() {
    return `${this.name}: ${this.error_code} with extra: ${JSON.stringify(
      this.extra ?? {}
    )}`;
  }
}

export class HandledError extends Error {
  constructor(
    message?: string,
    options?: ErrorOptions,
    public readonly error_handled: boolean = true
  ) {
    super(message, options);
  }
}

if (import.meta.vitest) {
  const { it, expect } = import.meta.vitest;

  it("should show domain error by its props", () => {
    let e = new DomainError("task_type_not_supported", {
      task_type: "test_type",
    });

    expect(e.toString()).toBe(
      'DomainError: task_type_not_supported with extra: {"task_type":"test_type"}'
    );

    e = new DomainError("task_type_not_supported");

    expect(e.toString()).toBe(
      "DomainError: task_type_not_supported with extra: {}"
    );
  });
}
