import { DomainError } from "@/lib/domain/common/error";
import { sessionSchema } from "@/lib/domain/user/m";
import type { NextAuthResult } from "next-auth";
import { redirect } from "next/navigation";

export class SessionSvc {
  constructor(private readonly nextAuth: NextAuthResult) {}

  async getSession() {
    const session = await this.nextAuth.auth();
    const { success, data: sessionParsed } = sessionSchema.safeParse(session);

    const now = Date.now();
    if (!success || !sessionParsed || now > sessionParsed.expires.getTime()) {
      throw new DomainError("auth_failed");
    }

    return sessionParsed;
  }

  async ensureLogin(options?: { redirect?: string }) {
    try {
      const session = await this.getSession();
      return session.user;
    } catch (err) {
      if (err instanceof DomainError && err.error_code == "auth_failed") {
        if (options?.redirect) {
          redirect(options.redirect);
        }
      }

      throw err;
    }
  }
}
