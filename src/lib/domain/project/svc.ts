import type { PrismaClient } from "@prisma/client";
import type { Operator } from "@/lib/domain/user/m";
import { z } from "zod";
import { DomainError } from "@/lib/domain/common/error";

export const createProjectInputSchema = z.object({
  name: z.string().min(1).max(30),
  remark: z.string().max(300).optional(),
});

export const updateProjectInputSchema = createProjectInputSchema.extend({
  projectId: z.string(),
});

export const deleteProjectInputSchema = z.object({
  projectId: z.string(),
});

type CreateProjectInput = z.infer<typeof createProjectInputSchema>;

type UpdateProjectInput = z.infer<typeof updateProjectInputSchema>;

type DeleteProjectInput = z.infer<typeof deleteProjectInputSchema>;

export class ProjectSvc {
  async createProject(
    prisma: PrismaClient,
    operator: Operator,
    { name, remark }: CreateProjectInput
  ) {
    await prisma.project.create({
      data: {
        name,
        remark,
        created_by_id: operator.id,
        updated_by_id: operator.id,
      },
    });
  }

  async getProject(prisma: PrismaClient, projectId: string) {
    const project = await prisma.project.findUnique({
      where: { id: projectId, deleted_at: 0 },
    });

    if (!project) {
      throw new DomainError("project_not_found");
    }

    return project;
  }

  async updateProject(
    prisma: PrismaClient,
    operator: Operator,
    { name, remark, projectId }: UpdateProjectInput
  ) {
    await prisma.project.update({
      where: { id: projectId, deleted_at: 0 },
      data: {
        name,
        remark,
        updated_by_id: operator.id,
      },
    });
  }

  async deleteProject(
    prisma: PrismaClient,
    operator: Operator,
    { projectId }: DeleteProjectInput
  ) {
    await prisma.project.update({
      where: { id: projectId, deleted_at: 0 },
      data: {
        deleted_at: Date.now(),
        updated_by_id: operator.id,
      },
    });
  }
}
