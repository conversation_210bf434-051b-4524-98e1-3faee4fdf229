import type { Decimal } from "@prisma/client/runtime/library";

export type ATPBalance = {
  user_id: string;
  atp: Decimal;
  version: string;
};

export const AtpPerFen = 0.01;

export const AtpUnit = 1000000;

export enum RechargeOrderStatus {
  Pending = "pending",
  PaidSuccess = "paid_success",
  PaidFailed = "paid_failed",
  Recharged = "recharged",
}

export type ConsumptionExtra = {
  thread_id: string;
  message_id: string;
  model: string;
  provider: string;
  pricing: { atpPerPromptToken: number; atpPerCompletionToken: number };
  token_usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
};

export type TransactionsSourceType = TransactionsSourceInfo["type"];

export type TransactionsSourceInfo =
  | {
      type: "recharge";
      info: {
        amount: number;
        atp: number;
      };
    }
  | {
      type: "token-consumation";
      info: {
        tokens: number;
        date_range: string;
      };
    };

export type WechatPayCallbackData = {
  id: string;
  create_time: string;
  resource_type: string;
  event_type: string;
  summary: string;
  resource: {
    original_type: string;
    algorithm: string;
    ciphertext: string;
    associated_data: string;
    nonce: string;
  };
};

export type HourlyTransaction = {
  user_id: string;
  atp: Decimal;
  source_info: TransactionsSourceInfo;
  source_type: TransactionsSourceType;
};

export type PreConsumption = {
  id: string;
  consumed_at: Date;
  atp: Decimal;
  tokens: Decimal;
};
