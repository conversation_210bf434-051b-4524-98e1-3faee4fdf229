export type ModelInfo = {
  id: IrsModelId;
  name: string;
  temperature: {
    min: number;
    max: number;
    value: number;
    step: number;
  };
  maxTokens: {
    min: number;
    max: number;
    value: number;
    step: number;
  };
};

export enum IrsModelId {
  DeepSeekR1 = "deepseek-r1",
  DeepSeekV3 = "deepseek-v3",
  Qwen3235BInstruct = "qwen3-235b-instruct",
  Qwen27BInstruct = "qwen2-7b-instruct",
}

export const Models: ModelInfo[] = [
  {
    id: IrsModelId.DeepSeekR1,
    name: "DeepSeek-R1",
    temperature: {
      min: 0,
      max: 2,
      value: 0.6,
      step: 0.1,
    },
    maxTokens: {
      min: 1,
      max: 16384,
      value: 8192,
      step: 1,
    },
  },
  {
    id: IrsModelId.DeepSeekV3,
    name: "DeepSeek-V3",
    temperature: {
      min: 0,
      max: 2,
      value: 0.7,
      step: 0.1,
    },
    maxTokens: {
      min: 1,
      max: 2048,
      value: 2048,
      step: 1,
    },
  },
  {
    id: IrsModelId.Qwen3235BInstruct,
    name: "Qwen3-235B-A22B",
    temperature: {
      min: 0,
      max: 2,
      value: 0.6,
      step: 0.1,
    },
    maxTokens: {
      min: 1,
      max: 8192,
      value: 8192,
      step: 1,
    },
  },
];

export const TopicModelId = IrsModelId.Qwen27BInstruct;
