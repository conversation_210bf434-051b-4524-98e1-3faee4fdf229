import { LanguageModelUsage, type CoreMessage } from "ai";
import { IrsModelId } from "@/lib/domain/ai/llm/models";

export type ChatRunOptions = {
  threadId: string;
  modelId: IrsModelId;
  messages: CoreMessage[];
  abortSignal?: AbortSignal;
  parentId?: string;
  assistantMsgId: string;
  maxTokens?: number;
  temperature?: number;
};

/**
 * this is an extra filed that might be used in frontend,
 * so don't inclue sensitive info
 * @property modelId - The identifier of the language model used.
 * @property tokenUsage - The token usage details for the language model.
 */
export type ModelUsage = {
  modelId: IrsModelId;
  tokenUsage?: LanguageModelUsage;
};
