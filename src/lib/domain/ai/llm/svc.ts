import type { ATPSvc } from "@/lib/domain/atp/svc";
import { DomainError } from "@/lib/domain/common/error";
import type { ThreadSvc } from "@/lib/domain/thread/svc";
import type { Operator } from "@/lib/domain/user/m";
import type { PrismaClient } from "@prisma/client";
import { streamText, type CoreAssistantMessage, type CoreMessage } from "ai";
import { ChatRunOptions, ModelUsage } from "@/lib/domain/ai/llm/m";
import { LLMPvdSvc } from "@/lib/domain/ai/provider/svc";
import { IrsModel } from "@/lib/domain/ai/llm/irs";

function generateAssistantMsg(
  reasoningText: string | undefined,
  chunkText: string,
  modelUsage: ModelUsage
): CoreAssistantMessage {
  const content = [];
  if (reasoningText && reasoningText.length > 0) {
    content.push({
      type: "reasoning",
      text: reasoningText,
    });
  }
  if (chunkText.length > 0) {
    content.push({
      type: "text",
      text: chunkText,
    });
  }

  const msg = {
    role: "assistant",
    content: content,
    // this is an extra filed that might be used in frontend, so don't inclue sensitive info
    modelUsage: modelUsage,
  };

  return msg as CoreAssistantMessage;
}

export class LLMSvc {
  constructor(
    private readonly threadSvc: ThreadSvc,
    private readonly atpSvc: ATPSvc,
    private readonly llmPvdSvc: LLMPvdSvc
  ) {}

  async chat(
    prisma: PrismaClient,
    operator: Operator,
    options: ChatRunOptions
  ) {
    const {
      abortSignal,
      parentId,
      assistantMsgId,
      threadId,
      modelId,
      messages,
      maxTokens,
      temperature,
    } = options;

    await this.atpSvc.ensureWithAtp(prisma, operator);

    const lastMsg = messages.findLast(
      (x) => x.role == "user"
    ) as CoreMessage & { unstable_id: string };
    const lastUserMsgId = lastMsg.unstable_id;

    if (!lastMsg || !lastUserMsgId) {
      throw new DomainError("arg_parse_failed");
    }

    this.threadSvc.updateThreadMessageAt(prisma, operator, threadId);

    const irsLLM = new IrsModel(this.llmPvdSvc, modelId, {
      user: operator.id,
    });
    const providerOptions = this.llmPvdSvc.getProviderOptions();

    await this.threadSvc.createThreadMessage(prisma, operator, {
      msgId: lastUserMsgId,
      msg: lastMsg,
      parentId,
      threadId,
      irsModelInfo: irsLLM.info,
    });

    let handledInFinished = false;
    let chunkText = "";
    let reasoningText = "";

    const result = streamText({
      model: irsLLM,
      messages,
      maxTokens,
      temperature,
      abortSignal,
      providerOptions,
      onChunk({ chunk }) {
        // only handle below two for now
        if (chunk.type == "text-delta") {
          chunkText += chunk.textDelta;
        }
        if (chunk.type === "reasoning") {
          reasoningText = chunk.textDelta;
        }
      },
      onFinish: async ({ text, usage: tokenUsage, reasoning }) => {
        handledInFinished = true;

        const msg = generateAssistantMsg(reasoning, text, {
          modelId: modelId,
          tokenUsage,
        });
        await this.threadSvc.createThreadMessage(prisma, operator, {
          msgId: assistantMsgId,
          parentId: lastUserMsgId,
          threadId,
          msg,
          irsModelInfo: irsLLM.info,
          tokenUsage: tokenUsage,
        });
      },
    });

    if (abortSignal) {
      abortSignal.onabort = async () => {
        if (handledInFinished) {
          return;
        }

        const msg = generateAssistantMsg(reasoningText, chunkText, {
          modelId: modelId,
        });
        await this.threadSvc.createThreadMessage(prisma, operator, {
          msgId: assistantMsgId,
          parentId: lastUserMsgId,
          threadId,
          msg,
          irsModelInfo: irsLLM.info,
        });
      };
    }

    return result;
  }
}
