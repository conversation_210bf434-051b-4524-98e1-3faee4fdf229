import { IrsModelId } from "@/lib/domain/ai/llm/models";
import { LLMPvdState, LLMPvdSvc } from "@/lib/domain/ai/provider/svc";
import { OpenAICompatibleChatSettings } from "@ai-sdk/openai-compatible";
import { LanguageModelV1, simulateReadableStream } from "ai";
import { MockLanguageModelV1 } from "ai/test";
import pRetry, { type RetryContext } from "p-retry";

export type IrsModelSetting = OpenAICompatibleChatSettings & {
  useMocked?: boolean;
};

export type LLMPricing = {
  atpPerPromptToken: number;
  atpPerCompletionToken: number;
};

export type IrsModelInfo = {
  irsModel: IrsModelId;
  provider: string;
  model: string;
  pricing: LLMPricing;
};

export class IrsModel implements LanguageModelV1 {
  pvd: LLMPvdState;
  private _inner_llm: LanguageModelV1;

  readonly specificationVersion = "v1";
  get provider() {
    return this._inner_llm.provider;
  }

  get modelId() {
    return this._inner_llm.modelId;
  }

  get defaultObjectGenerationMode() {
    return this._inner_llm.defaultObjectGenerationMode;
  }

  get supportsImageUrls() {
    return this._inner_llm.supportsImageUrls;
  }
  get supportsStructuredOutputs() {
    return this._inner_llm.supportsStructuredOutputs;
  }

  supportsUrl?(url: URL): boolean {
    return this._inner_llm.supportsUrl?.(url) || false;
  }

  get info(): IrsModelInfo {
    const pvdCfg = this.pvd.cfg;
    return {
      irsModel: this.irsModelId,
      provider: pvdCfg.provider,
      model: pvdCfg.model,
      pricing: pvdCfg.pricing,
    };
  }

  /**
   *
   */
  constructor(
    private readonly pvdSvc: LLMPvdSvc,
    readonly irsModelId: IrsModelId,
    readonly settings?: IrsModelSetting
  ) {
    this.pvd = this.pvdSvc.choosePvd(irsModelId);
    this._inner_llm = this.createInnerLLM();
  }

  async doStream(
    options: Parameters<LanguageModelV1["doStream"]>[0]
  ): Promise<Awaited<ReturnType<LanguageModelV1["doStream"]>>> {
    const res = await pRetry(
      async () => {
        return await this._inner_llm.doStream(options);
      },
      {
        maxTimeout: 1000,
        signal: options.abortSignal,
        shouldRetry: this.shouldRetry.bind(this),
        onFailedAttempt: this.onFailedAttempt.bind(this),
      }
    );

    return res;
  }

  async doGenerate(
    options: Parameters<LanguageModelV1["doGenerate"]>[0]
  ): Promise<Awaited<ReturnType<LanguageModelV1["doGenerate"]>>> {
    const res = await pRetry(
      async () => {
        return await this._inner_llm.doGenerate(options);
      },
      {
        maxTimeout: 1000,
        signal: options.abortSignal,
        shouldRetry: this.shouldRetry,
        onFailedAttempt: this.onFailedAttempt,
      }
    );
    return res;
  }

  private refreshPvd() {
    this.pvd = this.pvdSvc.choosePvd(this.irsModelId);
    this._inner_llm = this.createInnerLLM();
  }

  private createInnerLLM() {
    const mock = new MockLanguageModelV1({
      async doStream() {
        return {
          stream: simulateReadableStream({
            chunks: [
              {
                type: "reasoning",
                textDelta:
                  "Okay, the user said hi. That's pretty straightforward. \n```python\nimport a from xxx\ndef test():\n\t...\nprint('hello')\n```\nI should respond in a friendly and welcoming manner. Let me make sure to keep it open-ended so they feel comfortable to ask anything. Maybe add a smiley to keep it warm. Alright, here's the response.",
              },
              { type: "text-delta", textDelta: "Hello" },
              { type: "text-delta", textDelta: ", " },
              { type: "text-delta", textDelta: `world!` },
              {
                type: "text-delta",
                textDelta:
                  "\n```python\nimport a from xxx\ndef test():\n\t...\nprint('hello')\n```",
              },
              {
                type: "finish",
                finishReason: "stop",
                logprobs: undefined,
                usage: { completionTokens: 10, promptTokens: 3 },
              },
            ],
            chunkDelayInMs: 1000,
          }),
          rawCall: { rawPrompt: null, rawSettings: {} },
        };
      },
    });

    return this.settings?.useMocked ? mock : this.pvd.chatModel(this.settings);
  }

  onFailedAttempt(ctx: RetryContext) {
    console.error("model request error: ", ctx);
    this.pvd.markAsUnavailable();
    this.refreshPvd();
  }

  shouldRetry(ctx: RetryContext) {
    const error = ctx.error;
    const retryableErrors = [
      "overloaded",
      "service unavailable",
      "bad gateway",
      "too many requests",
      "internal server error",
      "gateway timeout",
      "rate_limit",
      "unexpected",
      "capacity",
      "timeout",
      "server_error",
      "429", // Too Many Requests
    ];

    const retryableStatusCodes = [
      408, // request timeout
      409, // conflict
      413, // payload too large
      429, // too many requests/rate limits
    ];

    if ("statusCode" in error) {
      const statusCode = error["statusCode"] as number;
      const hitStatusCodeMatch =
        statusCode &&
        (retryableStatusCodes.includes(statusCode) || statusCode >= 500);

      if (hitStatusCodeMatch) {
        return true;
      }
    }

    const errorString = error.message.toLowerCase();
    const hitErrorMatch = retryableErrors.some((errType) =>
      errorString.includes(errType)
    );

    return hitErrorMatch;
  }
}

if (import.meta.vitest) {
  const { it, expect, vi } = import.meta.vitest;
  const { mock } = await import("vitest-mock-extended");

  it("should throw when error doesn't need to retry", async () => {
    const logSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    const pvdSvc = mock<LLMPvdSvc>();
    const pvd1 = mock<LLMPvdState>();
    const llm1 = mock<LanguageModelV1>();
    pvd1.chatModel.mockReturnValue(llm1);
    pvdSvc.choosePvd.mockReturnValue(pvd1);
    llm1.doStream.mockRejectedValue(new Error("xxx"));
    const m = new IrsModel(pvdSvc, IrsModelId.DeepSeekR1);

    await expect(m.doStream({} as never)).rejects.toThrow("xxx");

    expect(logSpy).toHaveBeenCalledWith(
      "model request error: ",
      expect.anything()
    );
  });

  it("should fail over to anohter pvd when error needs to retry", async () => {
    const logSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    const pvdSvc = mock<LLMPvdSvc>();
    const pvd1 = mock<LLMPvdState>();
    const llm1 = mock<LanguageModelV1>();
    pvd1.chatModel.mockReturnValue(llm1);
    llm1.doStream.mockRejectedValue(new Error("TOO many Requests"));

    const pvd2 = mock<LLMPvdState>();
    const llm2 = mock<LanguageModelV1>();
    pvd2.chatModel.mockReturnValue(llm2);
    llm2.doStream.mockResolvedValue("do-stream-res" as never);

    pvdSvc.choosePvd.mockReturnValueOnce(pvd1).mockReturnValueOnce(pvd2);

    const m = new IrsModel(pvdSvc, IrsModelId.DeepSeekR1);

    const res = await m.doStream({} as never);

    expect(res).toBe("do-stream-res");
    expect(logSpy).toHaveBeenCalledWith(
      "model request error: ",
      expect.objectContaining({
        error: expect.objectContaining({
          message: "TOO many Requests",
        }),
      })
    );
  });
}
