import path from "node:path";
import type { AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { Server as TusServer } from "@tus/server";
import { S3Store } from "@tus/s3-store";
import { z } from "zod";
import { DiskPath } from "@/lib/domain/disk/svc";

const IRSMetaSchema = z.object({
  relativePath: z.string(),
  projectId: z.string(),
});

type IRSMeta = z.infer<typeof IRSMetaSchema>;

export class TusSvc {
  public tusServer: TusServer;

  constructor(private readonly appConfig: AppConfig) {
    this.tusServer = new TusServer({
      path: "/api/upload",
      namingFunction,
      generateUrl,
      getFileIdFromRequest,
      datastore: new S3Store({
        s3ClientConfig: {
          region: appConfig.OSS_REGION,
          bucket: appConfig.OSS_BUCKET,
          endpoint: appConfig.OSS_ENDPOINT,
          credentials: {
            accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
            secretAccessKey: appConfig.OSS_SECRET_ACCESS_KEY,
          },
          requestChecksumCalculation: "WHEN_REQUIRED",
          responseChecksumValidation: "WHEN_REQUIRED",
        },
      }),
    });
  }
}

function generatePath({ projectId, relativePath }: IRSMeta) {
  const ossPath = new DiskPath({ projectId }, relativePath);
  return ossPath.toOSSKey();
}

function extractMetaDataFromReq(req: Request) {
  const metaStr = req.headers.get("irs-meta") ?? "";
  try {
    const metaData = Buffer.from(metaStr, "base64").toString("utf-8");
    const meta = IRSMetaSchema.parse(JSON.parse(metaData));
    return meta;
  } catch {
    throw new DomainError("arg_parse_failed", { meta: metaStr });
  }
}

function namingFunction(req: Request) {
  const meta = extractMetaDataFromReq(req);
  const ossPath = generatePath(meta);
  return ossPath;
}

function generateUrl(req: Request, { path: uploadPath }: { path: string }) {
  const { projectId, relativePath } = extractMetaDataFromReq(req);

  return `${uploadPath}/${encodeURIComponent(
    path.join(projectId, relativePath)
  )}`;
}

function getFileIdFromRequest(req: Request) {
  const meta = extractMetaDataFromReq(req);
  const ossPath = generatePath(meta);
  return ossPath;
}
