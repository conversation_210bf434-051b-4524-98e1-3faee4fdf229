import { z } from "zod";

export const operatorSchema = z.object({
  id: z.string(),
  isTestAccount: z.boolean(),
});

export type Operator = z.infer<typeof operatorSchema>;

export const sessionSchema = z.object({
  user: z
    .object({
      id: z.string(),
      name: z.string(),
      redacted_phone: z.string(),
    })
    .transform((user) => ({
      ...user,
      isTestAccount: ["cm8sdsogk0000s501j670t8kv"].includes(user.id),
    })),
  expires: z.coerce.date(),
});

export type SessionUser = z.infer<typeof sessionSchema>["user"];

export type RawIdCardInfo = {
  realName: string;
  cardNo: string;
};

export type UserProfile = {
  redacted_id_card_no: string | null;
  redacted_id_card_name: string | null;
  id_card_verified: Date | null;
  name: string | null;
};
