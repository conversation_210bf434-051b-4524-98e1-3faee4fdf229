import dayjs from "dayjs";
import { DomainError } from "@/lib/domain/common/error";
import type { AliyunClient } from "@/lib/infra/aliyun/client";
import type { PrismaClient } from "@prisma/client";
import type { RawIdCardInfo, Operator, UserProfile } from "@/lib/domain/user/m";
import type { EncryptionSvc } from "@/lib/domain/encryption/svc";

export class UserSvc {
  constructor(
    private readonly aliyunClient: AliyunClient,
    private readonly encryptionSvc: EncryptionSvc
  ) {}

  async profile(prisma: PrismaClient, operator: Operator) {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: operator.id },
      select: {
        redacted_id_card_no: true,
        redacted_id_card_name: true,
        id_card_verified: true,
        name: true,
      },
    });

    return user as UserProfile;
  }

  async verifyIdCard(
    prisma: PrismaClient,
    operator: Operator,
    { realName, cardNo }: RawIdCardInfo
  ) {
    const birth =
      cardNo.slice(6, 10) +
      "-" +
      cardNo.slice(10, 12) +
      "-" +
      cardNo.slice(12, 14);

    // 14 years old or older
    if (!dayjs(birth).isBefore(dayjs().subtract(14, "year"))) {
      throw new DomainError("id_verify_error");
    }

    await this.aliyunClient.identityVerify(realName, cardNo);

    const redactedInfo = this.redactIdCardInfo({
      realName,
      cardNo,
    });

    await prisma.user.update({
      where: { id: operator.id },
      data: {
        id_card_info: redactedInfo.encryptedIdCardInfo,
        redacted_id_card_no: redactedInfo.redactedIdCardNo,
        redacted_id_card_name: redactedInfo.redactedIdCardName,
        id_card_verified: new Date(),
      },
    });
  }

  async updateUsername(prisma: PrismaClient, operator: Operator, name: string) {
    const users = await prisma.user.findMany({
      where: { name },
    });

    if (
      users.length === 0 ||
      (users.length === 1 && users[0].id === operator.id)
    ) {
      await prisma.user.update({
        where: { id: operator.id },
        data: { name },
      });
    } else {
      throw new DomainError("username_already_exists");
    }
  }

  redactIdCardInfo(info: RawIdCardInfo) {
    const { realName, cardNo } = info;

    const encryptedIdCardInfo = this.encryptionSvc.encrypt(
      JSON.stringify(info)
    );

    const redactedIdCardNo =
      cardNo.slice(0, 4) + "*".repeat(cardNo.length - 8) + cardNo.slice(-4);

    let redactedIdCardName = "";
    if (realName.length === 2) {
      redactedIdCardName = realName[0] + "*";
    } else if (realName.length > 2) {
      const stars = "*".repeat(realName.length - 2);
      redactedIdCardName = realName[0] + stars + realName.slice(-1);
    } else {
      redactedIdCardName = "*";
    }

    return {
      encryptedIdCardInfo,
      redactedIdCardNo,
      redactedIdCardName,
    };
  }
}
