import { IrsModelInfo } from "@/lib/domain/ai/llm/irs";
import type { ATPSvc } from "@/lib/domain/atp/svc";
import type { ChatModelConfig, ThreadCreateDto } from "@/lib/domain/thread/m";
import type { Operator } from "@/lib/domain/user/m";
import type { PrismaClient } from "@prisma/client";
import type { InputJsonValue } from "@prisma/client/runtime/library";
import type { CoreMessage, LanguageModelUsage } from "ai";

export class ThreadSvc {
  constructor(private readonly atpSvc: ATPSvc) {}

  async updateThreadTitle(
    prisma: PrismaClient,
    dto: { threadId: string; title: string },
    operator: Operator
  ) {
    const title = dto.title.slice(0, 32);
    if (title.length == 0) {
      return;
    }

    await prisma.thread.update({
      where: {
        id: dto.threadId,
        user_id: operator.id,
      },
      data: {
        title: title,
      },
    });
  }

  async createThread(
    prisma: PrismaClient,
    operator: Operator,
    data: ThreadCreateDto
  ) {
    const result = await prisma.thread.create({
      data: {
        user_id: operator.id,
        title: data.title,
        last_message_at: new Date(),
        metadata: data.metadata as InputJsonValue,
      },
      select: {
        id: true,
      },
    });

    return result;
  }

  async updateThreadMessageAt(
    prisma: PrismaClient,
    operator: Operator,
    threadId: string
  ) {
    await prisma.thread.update({
      where: {
        id: threadId,
        user_id: operator.id,
      },
      data: {
        last_message_at: new Date(),
      },
    });
  }

  async updateThreadModelConfig(
    prisma: PrismaClient,
    operator: Operator,
    threadId: string,
    config: ChatModelConfig
  ) {
    await prisma.thread.update({
      where: {
        id: threadId,
        user_id: operator.id,
      },
      data: { model_config: config },
    });
  }

  async createThreadMessage(
    prisma: PrismaClient,
    operator: Operator,
    {
      msgId,
      msg,
      parentId,
      threadId,
      irsModelInfo,
      tokenUsage,
    }: {
      msgId: string;
      msg: CoreMessage;
      parentId?: string;
      threadId: string;
      irsModelInfo: IrsModelInfo;
      tokenUsage?: LanguageModelUsage;
    }
  ) {
    if (tokenUsage) {
      await this.atpSvc.consumeTokens(prisma, operator, {
        threadId: threadId,
        msgId: msgId,
        tokenUsage: tokenUsage,
        irsModelInfo: irsModelInfo,
      });
    }

    const createdMsg = await prisma.message.upsert({
      where: {
        thread_id_id: {
          thread_id: threadId,
          id: msgId,
        },
      },
      create: {
        id: msgId,
        thread_id: threadId,
        parent_id: parentId,
        content: msg as InputJsonValue,
      },
      update: {},
    });

    return createdMsg;
  }
}
