import { type AppConfig } from "@/lib/app-config";
import {
  importKeychainSync,
  encryptStringSync,
  generateKey,
  exportKeychainSync,
  type CloakKeychain,
  type CloakedString,
  type C<PERSON><PERSON><PERSON><PERSON>,
} from "@47ng/cloak";

export class EncryptionSvc {
  constructor(private readonly appConfig: AppConfig) {}

  getCurrentKeychain(): CloakKeychain {
    const keychain = importKeychainSync(
      this.appConfig.CLOAK_KEYCHAIN,
      this.appConfig.CLOAK_MASTER_KEY
    );
    return keychain;
  }

  encrypt(input: string): CloakedString {
    const keychain = this.getCurrentKeychain();
    const currentKey = keychain[this.appConfig.CLOAK_CURRENT_KEY].key;
    const encryptedString = encryptStringSync(input, currentKey);
    return encryptedString;
  }

  rotateMasterKey(key?: CloakKey): {
    masterKey: string;
    keychain: CloakKey;
  } {
    const keychain = this.getCurrentKeychain();
    const newMasterKey = key || generateKey();
    const newCloakedKeychain = exportKeychainSync(keychain, newMasterKey);

    return {
      masterKey: newMasterKey,
      keychain: newCloakedKeychain,
    };
  }
}
