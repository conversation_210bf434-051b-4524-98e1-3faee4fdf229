import { S3Client } from "@/lib/infra/s3/client";
import { z } from "zod";
import path from "path";
import { DomainError } from "@/lib/domain/common/error";
import { DiskDirNameReg, DiskObjectNameReg } from "@/lib/domain/disk/constants";
import { genUniqueName, isS3FileAlreadyExistsError } from "@/lib/utils";

export type FileInfo = {
  isDir: false;
  size?: number;
  lastModified?: Date;
  diskPath: string;
};

export type DirInfo = {
  isDir: true;
  diskPath: string;
};

export type ObjectInfo = FileInfo | DirInfo;

export const CreateDirInputSchema = z.object({
  projectId: z.string(),
  parentPath: z.string(),
  name: z.string().regex(DiskDirNameReg),
});

export const RemoveInputSchema = z.object({
  projectId: z.string(),
  pathPart: z.string(),
});

export const CopyToInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
  destinationPath: z.string(),
  forbidOverwrite: z.boolean().optional().default(false),
});

export const MoveToInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
  destinationPath: z.string(),
});

export const ListObjectsInputSchema = z.object({
  projectId: z.string(),
  prefix: z.string(),
});

export const CreateCopyInputSchema = z.object({
  projectId: z.string(),
  sourcePath: z.string(),
});

export const RenameInputSchema = z
  .object({
    projectId: z.string(),
    parentPath: z.string(),
    originName: z.string(),
    name: z.string(),
    isFile: z.boolean().optional(),
  })
  .superRefine(({ isFile, name }, ctx) => {
    const reg = isFile ? DiskObjectNameReg : DiskDirNameReg;
    if (!reg.test(name)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
      });
    }
  });

export type RemoveInput = z.infer<typeof RemoveInputSchema>;

export type CreateDirInput = z.infer<typeof CreateDirInputSchema>;

export type CopyToInput = z.infer<typeof CopyToInputSchema>;

export type MoveToInput = z.infer<typeof MoveToInputSchema>;

export type ListObjectsInput = z.infer<typeof ListObjectsInputSchema>;

export type CreateCopyInput = z.infer<typeof CreateCopyInputSchema>;

export type RenameInput = z.infer<typeof RenameInputSchema>;

export class DiskSvc {
  constructor(private readonly s3Client: S3Client) {}

  async *listFirstLevel({ projectId, prefix }: ListObjectsInput) {
    const diskPath = new DiskPath({ projectId }, prefix);

    const objects = this.s3Client.listInfiniteObject({
      prefix: diskPath.toOSSKey(),
      delimiter: "/",
    });

    for await (const { Contents, CommonPrefixes } of objects) {
      for (const { Prefix } of CommonPrefixes) {
        if (!Prefix) {
          continue;
        }

        const objInfo: ObjectInfo = {
          isDir: true,
          diskPath: diskPath.stripToRoot(Prefix),
        };
        yield objInfo;
      }

      for (const { Key, LastModified, Size } of Contents) {
        if (!Key || Key === diskPath.toOSSKey()) {
          // skip self as this function only list first level
          continue;
        }
        const objInfo: ObjectInfo = {
          isDir: false,
          size: Size,
          lastModified: LastModified,
          diskPath: diskPath.stripToRoot(Key),
        };
        yield objInfo;
      }
    }
  }

  async createDir({ projectId, parentPath, name }: CreateDirInput) {
    const ossPath = new DiskPath({ projectId }, parentPath, name).toOSSKey();
    const result = await this.s3Client.createDir(ossPath);
    return result;
  }

  async remove({ projectId, pathPart }: RemoveInput) {
    const diskPath = new DiskPath({ projectId }, pathPart);
    const isDirPath = isDir(diskPath.toOSSKey());

    if (isDirPath) {
      const objects = this.s3Client.listInfiniteObject({
        prefix: diskPath.toOSSKey(),
      });

      for await (const { Contents, CommonPrefixes } of objects) {
        const keys = Contents.map(({ Key }) => Key).filter(
          (key) => key !== undefined
        );

        if (keys.length > 0) {
          await this.s3Client.deleteObjects(keys);
        }

        const commonPrefixesKeys = CommonPrefixes.map(
          ({ Prefix }) => Prefix
        ).filter((key) => key !== undefined);

        if (commonPrefixesKeys.length > 0) {
          await this.s3Client.deleteObjects(commonPrefixesKeys);
        }
      }
    } else {
      await this.s3Client.deleteObject(diskPath.toOSSKey());
    }
  }

  private async batchCopyObjects({
    projectId,
    sourcePath,
    keyTransformer,
    forbidOverwrite = false,
  }: {
    projectId: string;
    sourcePath: string;
    keyTransformer: (sourceKey: string) => string;
    forbidOverwrite?: boolean;
  }) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const isDirPath = isDir(diskSrcPath);
    if (isDirPath) {
      const dirs = this.s3Client.listInfiniteObject({
        prefix: diskSrcPath,
      });

      for await (const { Contents } of dirs) {
        for (const content of Contents ?? []) {
          const { Key } = content;
          if (!Key) continue;

          const destinationKey = path.normalize(keyTransformer(Key));
          await this.s3Client.copyObject(Key, destinationKey, forbidOverwrite);
        }
      }
    } else {
      await this.s3Client.copyObject(
        diskSrcPath,
        path.normalize(keyTransformer(diskSrcPath)),
        forbidOverwrite
      );
    }
  }

  async copyTo({
    projectId,
    sourcePath,
    destinationPath,
    forbidOverwrite,
  }: CopyToInput) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const diskDstPath = new DiskPath({ projectId }, destinationPath).toOSSKey();
    const diskSrcParentPath = path.dirname(diskSrcPath);

    await this.batchCopyObjects({
      projectId,
      sourcePath,
      keyTransformer: (key) => key.replace(diskSrcParentPath, diskDstPath),
      forbidOverwrite,
    });
  }

  async moveTo({ projectId, sourcePath, destinationPath }: MoveToInput) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const diskDstPath = new DiskPath({ projectId }, destinationPath).toOSSKey();

    if (getParentFolderDiskPath(diskSrcPath) === diskDstPath) {
      // same level
      return;
    }

    if (diskDstPath.startsWith(diskSrcPath)) {
      throw new DomainError("forbid_to_move_to_sub_dir");
    }

    await this.copyTo({
      projectId,
      sourcePath,
      destinationPath,
      forbidOverwrite: true,
    });
    await this.remove({ projectId, pathPart: sourcePath });
  }

  async createCopy({ projectId, sourcePath }: CreateCopyInput) {
    const diskSrcPath = new DiskPath({ projectId }, sourcePath).toOSSKey();
    const diskSrcParentpath = path.dirname(diskSrcPath);
    const isDirPath = isDir(diskSrcPath);
    const srcName = path.basename(diskSrcPath);
    const uniqueName = genUniqueName(srcName, isDirPath);

    await this.batchCopyObjects({
      projectId,
      sourcePath,
      keyTransformer: (key) =>
        key.replace(
          diskSrcPath,
          path.join(diskSrcParentpath, "/", uniqueName, isDirPath ? "/" : "")
        ),
      forbidOverwrite: true,
    });
  }

  async rename({ projectId, parentPath, originName, name }: RenameInput) {
    const parentDiskPath = new DiskPath({ projectId }, parentPath).toOSSKey();
    const originDiskPath = new DiskPath({ projectId }, parentPath, originName);
    const isDirPath = isDir(originDiskPath.toOSSKey());

    try {
      await this.batchCopyObjects({
        projectId,
        sourcePath: path.join(parentPath, originName),
        keyTransformer: (key) =>
          key.replace(
            originDiskPath.toOSSKey(),
            path.join(parentDiskPath, name, isDirPath ? "/" : "")
          ),
        forbidOverwrite: true,
      });
    } catch (error) {
      if (isS3FileAlreadyExistsError(error)) {
        throw new DomainError("disk_object_already_exists");
      }
      throw error;
    }

    await this.remove({
      projectId,
      pathPart: originDiskPath.toTreeNodePath(),
    });
  }
}

export class DiskPath {
  private readonly paths: string[];
  private readonly projectId: string;

  constructor({ projectId }: { projectId: string }, ...paths: string[]) {
    this.projectId = projectId;
    this.paths = paths;
  }

  toOSSKey() {
    return path.join(this.rootPath, ...this.paths);
  }

  toTreeNodePath() {
    return this.stripToRoot(this.toOSSKey());
  }

  get rootPath() {
    const baseRootPath = `project/${this.projectId}/disk/`;
    return baseRootPath;
  }

  stripToRoot(ossKey: string) {
    return ossKey.replace(this.rootPath, "/");
  }
}

export function isDir(path: string) {
  return path.endsWith("/");
}

export function getParentFolderDiskPath(diskPath: string) {
  const parentDiskPath = path.join(path.dirname(diskPath), "/");
  return parentDiskPath;
}

if (import.meta.vitest) {
  const { it, expect, describe } = import.meta.vitest;

  describe("isDir", () => {
    it("normal file name", () => {
      expect(isDir("a.txt")).toBe(false);
    });

    it("normal folder name", () => {
      expect(isDir("dirA/")).toBe(true);
    });
  });

  describe("RenameInputSchema", () => {
    it("should throw err when file", () => {
      const testParseFunc = () =>
        RenameInputSchema.parse({
          projectId: "project-1",
          parentPath: "dirA",
          originName: "fileA.txt",
          name: "^^&.txt",
          isFile: true,
        });
      expect(testParseFunc).toThrow();
    });
  });
  it("validate file normally", () => {
    const testParseFunc = () =>
      RenameInputSchema.parse({
        projectId: "project-1",
        parentPath: "dirA",
        originName: "fileA.txt",
        name: "3.txt",
        isFile: true,
      });
    expect(testParseFunc).not.toThrow();
  });

  it("should throw err when folder", () => {
    const testParseFunc = () =>
      RenameInputSchema.parse({
        projectId: "project-1",
        parentPath: "dirA",
        originName: "folderA",
        name: "3.txt",
      });
    expect(testParseFunc).toThrow();
  });

  it("validate folder normally", () => {
    const testParseFunc = () =>
      RenameInputSchema.parse({
        projectId: "project-1",
        parentPath: "dirA",
        originName: "folderA",
        name: "3Folder",
        isFile: true,
      });
    expect(testParseFunc).not.toThrow();
  });
}
