import { cacheLazySync } from "@/lib/lazy";

import <PERSON>s, { type TusOpts } from "@uppy/tus";
import Uppy, { type Meta, type Body } from "@uppy/core";

const onBeforeRequest: TusOpts<Meta, Body>["onBeforeRequest"] = (req, file) => {
  req.setHeader(
    "irs-meta",
    Buffer.from(JSON.stringify(file.meta)).toString("base64")
  );
};

export const getUppy = cacheLazySync(() => {
  const uppy = new Uppy().use(Tus, {
    endpoint: "/api/upload",
    onBeforeRequest,
  });
  return uppy;
});
