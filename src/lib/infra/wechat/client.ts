import { loadAppConfig, type AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { Output, Payment } from "wechatpay-nextjs-v3";

export class WeChatPayClient {
  client: Payment;
  constructor(private appConfig: AppConfig) {
    this.client = new Payment({
      appid: appConfig.WECHAT_PAY_APP_ID,
      mchid: appConfig.WECHAT_PAY_MACH_ID,
      key: appConfig.WECHAT_PAY_API_KEY,
      privateKey: Buffer.from(appConfig.WECHAT_PAY_PVK, "utf8"),
      publicKey: Buffer.from(appConfig.WECHAT_PAY_PBK, "utf8"),
    });
  }

  async generatePayQRCode(order_id: string, amountByFen: number, desc: string) {
    const params = {
      out_trade_no: order_id,
      description: desc,
      notify_url: this.appConfig.WECHAT_PAY_CALLBACK_URL,
      amount: {
        total: amountByFen,
      },
    };
    const res = await this.client.transactions_native(params);
    const data = ensureStatusWithSucceed(res);
    return data.code_url as string;
  }

  async queryOrderPayInfo(order_id: string) {
    const res = await this.client.query({ out_trade_no: order_id });
    const data = ensureStatusWithSucceed(res);
    return data as QueryPayInfo;
  }

  decipher(cipher: {
    ciphertext: string;
    associated_data: string;
    nonce: string;
  }) {
    const decipherRes = this.client.decipher_gcm<CallbackPayInfo>(
      cipher.ciphertext,
      cipher.associated_data,
      cipher.nonce
    );
    return decipherRes;
  }
}

function ensureStatusWithSucceed(res: Output) {
  if (res.status !== 200) {
    throw new DomainError("payclient_error", res.data);
  }

  return res.data;
}

export type QueryPayInfo =
  | (CallbackPayInfo & {
      trade_state: "SUCCESS";
    })
  | {
      trade_state: string;
      transaction_id: string;
      success_time: string;
    };

export type CallbackPayInfo = {
  amount: {
    currency: string;
    payer_currency: string;
    payer_total: number;
    total: number;
  };
  appid: string;
  attach: string;
  bank_type: string;
  mchid: string;
  out_trade_no: string;
  payer: {
    openid: string;
  };
  success_time: string;
  trade_state: string;
  trade_state_desc: string;
  trade_type: string;
  transaction_id: string;
};

if (import.meta.vitest) {
  const { it, expect } = import.meta.vitest;

  const cd = {
    id: "2e3f9217-7b2e-5e77-95b4-4456a3109d98",
    create_time: "2025-03-29T19:04:17+08:00",
    resource_type: "encrypt-resource",
    event_type: "TRANSACTION.SUCCESS",
    summary: "支付成功",
    resource: {
      original_type: "transaction",
      algorithm: "AEAD_AES_256_GCM",
      ciphertext:
        "RjHnZp8KBtmVcS9Bnlscvn5H+eyUjUh5+fZuJlSltNGVmurdiZZWs+38fOal5ly12Em/3Gxd9EO627thcrZPpVBBIuVc0hjUg2PobM/n5ju2PXU5e+ROkt3PPpQYecSJzePnnY9XKbOwFj5JwQIPZxg1bkikOHhDZXWgHn0bqgx7I93NcWEq/XQmlX92s7a7Hb/FzZx9LTlj/wptuet8ZcBLuDInv6NPbPCk6Hz2P+8VLMH033K6ueeWH8Dm888MD5xtUmPm+M62wXI9bi2sn1Uy0zCk3SWwN9f1Yk1qZTXUhx5ng3zJVUhciPE3NbqNlr+h3FGpYHlvUH+LUDlR7SPU45brNeWg3tseWF02Ftrj63B3aF5QN43r5Hitn4tYjHUfTOyQFmaF14s+i+z5hbHiC+riwm/Kq0rdrB0PJWF9MMdM+t4FjcyQd8QWoHhoIairWpB88w0zzKoYVGhBMQD6YeRsOh2L9HPc2n8PeMsIgFMCoRy+sxQOkeBwFC1+XbmOMPSctSd17GeEL7ytr845Rg+SO7JV2Ic0/+r+8IRiQ1AB/CegsMG7c3fv0vrsdyltO6OgErNn",
      associated_data: "transaction",
      nonce: "80JAGURxSHTj",
    },
  };

  it("should decipher with success", async () => {
    const config = await loadAppConfig();
    if (!config.WECHAT_PAY_API_KEY) {
      return;
    }

    const pay = new WeChatPayClient(config);
    const res = pay.decipher(cd.resource);

    expect(res.trade_state).toBe("SUCCESS");
  });
}
