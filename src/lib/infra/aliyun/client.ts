import { type AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { Client } from "aliyun-api-gateway";

export type IdVerifyResponse = {
  error_code: number;
  reason: string;
  result: {
    realname: string;
    idcard: string;
    isok: boolean;
    IdCardInfor: {
      province: string;
      city: string;
      district: string;
      area: string;
      sex: string;
      birthday: string;
    };
  };
};

export class AliyunClient {
  private client: InstanceType<typeof Client>;
  constructor(private appConfig: AppConfig) {
    this.client = new Client(
      this.appConfig.ALI_AUTH_APP_KEY,
      this.appConfig.ALI_AUTH_APP_SECRET
    );
  }

  async identityVerify(
    realName: string,
    cardNo: string
  ): Promise<IdVerifyResponse["result"]> {
    try {
      const res = await this.client.get(
        "https://zidv2.market.alicloudapi.com/idcard/VerifyIdcardv2",
        {
          headers: { accept: "application/json" },
          query: { cardNo, realName },
        }
      );

      const data = ensureStatusWithSucceed(JSON.parse(res));
      return data;
    } catch (err) {
      throw err instanceof DomainError
        ? err
        : new DomainError("id_verify_error", err);
    }
  }
}

function ensureStatusWithSucceed(res: IdVerifyResponse) {
  if (res.error_code === 0 && res.result.isok === true) {
    return res.result;
  }

  throw new DomainError("id_verify_error", res);
}
