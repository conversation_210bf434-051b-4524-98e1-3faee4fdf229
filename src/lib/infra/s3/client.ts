import { S3 } from "@aws-sdk/client-s3";
import OSS from "ali-oss";
import type { AppConfig } from "@/lib/app-config";

export class S3Client {
  private readonly s3Client: S3;
  private readonly ossClient: OSS;

  readonly bucket: string;

  constructor(private readonly appConfig: AppConfig) {
    this.bucket = appConfig.OSS_BUCKET;
    this.s3Client = new S3({
      region: appConfig.OSS_REGION,
      endpoint: appConfig.OSS_ENDPOINT,
      credentials: {
        accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
        secretAccessKey: appConfig.OSS_SECRET_ACCESS_KEY,
      },
      requestChecksumCalculation: "WHEN_REQUIRED",
      responseChecksumValidation: "WHEN_REQUIRED",
    });

    this.ossClient = new OSS({
      region: appConfig.OSS_REGION,
      bucket: appConfig.OSS_BUCKET,
      accessKeyId: appConfig.OSS_ACCESS_KEY_ID,
      accessKeySecret: appConfig.OSS_SECRET_ACCESS_KEY,
    });
  }

  /**
   * @param prefix should end with /, like: cm3aa/xxx/
   * @see https://help.aliyun.com/zh/oss/developer-reference/list-objects-5?spm=a2c4g.11186623.help-menu-31815.d_5_2_10_6_2_2.67fa45da8kCC9n&scm=20140722.H_111389._.OR_help-T_cn~zh-V_1#section-zq7-oi5-byh
   */
  async *listInfiniteObject(
    {
      prefix,
      delimiter,
    }: {
      prefix: string;
      delimiter?: string;
    },
    options?: HttpHandlerOptions
  ) {
    let nextContinuationToken: string | undefined;

    while (true) {
      const result = await this.s3Client.listObjectsV2(
        {
          Bucket: this.bucket,
          Prefix: prefix,
          Delimiter: delimiter,
          MaxKeys: 1000,
          ContinuationToken: nextContinuationToken,
        },
        options
      );

      yield {
        Contents: result.Contents ?? [],
        CommonPrefixes: result.CommonPrefixes ?? [],
      };

      if (result.NextContinuationToken) {
        nextContinuationToken = result.NextContinuationToken;
      } else {
        return;
      }
    }
  }

  async createDir(key: string) {
    if (!key.endsWith("/")) {
      key = key + "/";
    }
    const result = await this.s3Client.putObject({
      Bucket: this.bucket,
      Key: key,
      Body: Buffer.from(""),
    });
    return result;
  }

  async copyObject(
    sourceKey: string,
    destinationKey: string,
    forbidOverwrite: boolean = false
  ) {
    if (forbidOverwrite) {
      const result = await this.ossClient.copy(destinationKey, sourceKey, {
        headers: {
          "x-oss-forbid-overwrite": "true",
        },
      });
      return result;
    }
    const result = await this.s3Client.copyObject({
      Bucket: this.bucket,
      CopySource: encodeURIComponent(`${this.bucket}/${sourceKey}`),
      Key: destinationKey,
    });

    return result;
  }

  async deleteObject(key: string) {
    const result = await this.s3Client.deleteObject({
      Bucket: this.bucket,
      Key: key,
    });

    return result;
  }

  async deleteObjects(keys: string[]) {
    const res = await this.s3Client.deleteObjects({
      Bucket: this.bucket,
      Delete: {
        Objects: keys.map((key) => ({ Key: key })),
      },
    });
    return res;
  }

  async totalSizeInBytes(key: string, options?: HttpHandlerOptions) {
    let totalSizeInBytes = 0;
    for await (const { Contents } of this.listInfiniteObject(
      { prefix: key },
      options
    )) {
      for (const content of Contents) {
        totalSizeInBytes += content.Size ?? 0;
      }
    }

    return totalSizeInBytes;
  }
}

type HttpHandlerOptions = Parameters<S3["headObject"]>[1];
