import { type AppConfig } from "@/lib/app-config";
import { AuthenticationClient, ManagementClient } from "authing-node-sdk";

export class AuthingClient {
  aclient: AuthenticationClient;
  mclient: ManagementClient;

  constructor(private appConfig: AppConfig) {
    this.aclient = new AuthenticationClient({
      appId: appConfig.AUTHING_ID,
      appSecret: appConfig.AUTHING_SECRET,
      appHost: appConfig.AUTHING_APP_HOST,
    });

    this.mclient = new ManagementClient({
      accessKeyId: appConfig.AUTHING_USER_POOL_ID,
      accessKeySecret: appConfig.AUTHING_USER_POOL_SECRET,
    });
  }
}
