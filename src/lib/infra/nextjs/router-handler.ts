import { DomainError, type DomainErrorCode } from "@/lib/domain/common/error";

export type ServerError = {
  error_code: DomainErrorCode;
};

export function handleDomainError(err: Error) {
  if (err instanceof DomainError) {
    let statusCode;

    const error_code = err.error_code;

    switch (error_code) {
      case "auth_failed":
        statusCode = 401;
        break;
      case "no_permission":
        statusCode = 403;
        break;
      default:
        statusCode = 400;
    }

    const jsonData: ServerError = {
      error_code: error_code,
    };
    return { jsonData, statusCode };
  } else {
    throw err;
  }
}

type RouteHandle = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ...args: any[]
) => void | Response | Promise<void | Response>;

export function createUnifiedAPIRouteHandler(
  handler: RouteHandle
): RouteHandle {
  return async (...args) => {
    try {
      const res = await handler(...args);
      return res;
    } catch (err) {
      const { jsonData, statusCode } = handleDomainError(err as Error);
      return Response.json(jsonData, { status: statusCode });
    }
  };
}
