import "server-only";

import { z } from "zod";
import { loadConfig } from "zod-config";

const schemaConfig = z.object({
  AUTHING_ID: z.string(),
  AUTHING_SECRET: z.string(),
  AUTHING_APP_HOST: z.string(),
  AUTHING_USER_POOL_ID: z.string(),
  AUTHING_USER_POOL_SECRET: z.string(),

  SILICON_API_KEY: z.string(),
  ARK_API_KEY: z.string(),

  WECHAT_PAY_PBK: z.string(),
  WECHAT_PAY_PVK: z.string(),
  WECHAT_PAY_API_KEY: z.string(),
  WECHAT_PAY_APP_ID: z.string(),
  WECHAT_PAY_MACH_ID: z.string(),
  WECHAT_PAY_CALLBACK_URL: z.string(),

  ALI_AUTH_APP_KEY: z.string(),
  ALI_AUTH_APP_SECRET: z.string(),

  CLOAK_MASTER_KEY: z.string(),
  CLOAK_KEYCHAIN: z.string(),
  CLOAK_CURRENT_KEY: z.string(),

  AUTH_DEBUG: z.coerce.boolean(),

  OSS_REGION: z.string(),
  OSS_BUCKET: z.string(),
  OSS_ENDPOINT: z.string(),
  OSS_ACCESS_KEY_ID: z.string(),
  OSS_SECRET_ACCESS_KEY: z.string(),

  REMOTE_TASK_API_URL: z.string(),
  RESTATE_API_URL: z.string(),
});

export type AppConfig = z.infer<typeof schemaConfig>;

export async function loadAppConfig(): Promise<AppConfig> {
  // using default env (process.env)
  const appConfig = await loadConfig({
    schema: schemaConfig,
  });
  return appConfig;
}

if (import.meta.vitest) {
  const { it, expect } = import.meta.vitest;
  const appConfig = await loadAppConfig();
  it("should get from env", () => {
    expect(Object.keys(appConfig)).toContain("AUTHING_ID");
  });
}
