import { TaskDetail } from "@/app-ui/components/task/detail";
import { di } from "@/app-ui/di";

export default async function TaskPage({
  params,
}: {
  params: Promise<{ projectId: string; taskId: string }>;
}) {
  const { taskId } = await params;
  const svc = await di.getQuerySvc();
  const taskInfo = await svc.task(taskId);

  if (!taskInfo) {
    return <div>任务不存在</div>;
  }

  return <TaskDetail taskInfo={taskInfo} />;
}
