import { PrjAppPanel } from "@/app-ui/components/project/app-panel";
import { PrjHeader } from "@/app-ui/components/project/header";
import { PrjOverview } from "@/app-ui/components/project/overview";
import { PrjResource } from "@/app-ui/components/project/resource";
import { di } from "@/app-ui/di";
import { Splitter } from "antd";

export default async function ProjectPage({
  params,
}: {
  params: Promise<{ projectId: string }>;
}) {
  const { projectId } = await params;
  const prisma = await di.getPrisma();
  const projectSvc = await di.getProjectSvc();

  const project = await projectSvc.getProject(prisma, projectId);

  return (
    <Splitter className="h-screen">
      <Splitter.Panel min={400} max={800} defaultSize={400}>
        <div className="flex flex-col h-full">
          <div className="flex-none">
            <PrjHeader name={project.name} />
          </div>
          <div className="flex-auto border-b">
            <PrjResource projectId={projectId} />
          </div>
          <div className="flex-auto">
            <PrjAppPanel projectId={projectId} />
          </div>
        </div>
      </Splitter.Panel>
      <Splitter.Panel>
        <PrjOverview projectId={projectId} />
      </Splitter.Panel>
    </Splitter>
  );
}
