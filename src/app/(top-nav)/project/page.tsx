import { Skeleton } from "antd";
import { Suspense } from "react";
import { PrjList } from "@/app-ui/components/project/list";
import { di } from "@/app-ui/di";

async function Projects() {
  const querySvc = await di.getQuerySvc();
  const projects = await querySvc.projects();

  return <PrjList projects={projects} />;
}

export default async function ProjectsPage() {
  return (
    <div className="w-full px-6">
      <Suspense fallback={<Skeleton active />}>
        <Projects />
      </Suspense>
    </div>
  );
}
