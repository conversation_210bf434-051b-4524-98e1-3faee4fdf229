"use client";

import { useEffect, useRef } from "react";
import * as THREE from "three";

const ThreeParticles = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const effectController = useRef({
    showDots: true,
    showLines: true,
    minDistance: 150,
    limitConnections: false,
    maxConnections: 20,
    particleCount: 400,
  });

  const getContainerSize = () => {
    if (!containerRef.current) return { w: 800, h: 600 };
    const w = containerRef.current.clientWidth;
    const h = containerRef.current.clientHeight;

    const radio = w / h;
    // 实测宽高比小于0.845被遮挡了，需要调整高度
    return { w, h: radio < 0.845 ? w * 1.055 : h };
  };

  useEffect(() => {
    if (!containerRef.current) return;

    const maxParticleCount = 1000;
    const r = 800;
    const rHalf = r / 2;
    // Scene setup
    let camera: THREE.PerspectiveCamera;
    let scene: THREE.Scene;
    let renderer: THREE.WebGLRenderer | undefined;
    let group: THREE.Group;
    let particles: THREE.BufferGeometry;
    let pointCloud: THREE.Points;
    let linesMesh: THREE.LineSegments;
    const particlesData: {
      velocity: THREE.Vector3;
      numConnections: number;
    }[] = [];
    let particlePositions: Float32Array;
    let positions: Float32Array;
    let colors: Float32Array;

    // Initialization
    const init = () => {
      const { w, h } = getContainerSize();
      camera = new THREE.PerspectiveCamera(45, w / h, 1, 4000);
      camera.position.z = 1750;

      scene = new THREE.Scene();
      scene.background = null; // Set scene background to null for transparency

      group = new THREE.Group();
      scene.add(group);

      // Helper box
      const helper = new THREE.BoxHelper(
        new THREE.Mesh(new THREE.BoxGeometry(r, r, r))
      );
      helper.material.color.setHex(0x474747);
      helper.material.blending = THREE.AdditiveBlending;
      helper.material.transparent = true;
      group.add(helper);

      // Particles setup
      const segments = maxParticleCount * maxParticleCount;
      positions = new Float32Array(segments * 3);
      colors = new Float32Array(segments * 3);

      const pMaterial = new THREE.PointsMaterial({
        color: 0xffffff,
        size: 3,
        blending: THREE.AdditiveBlending,
        transparent: true,
        sizeAttenuation: false,
      });

      particles = new THREE.BufferGeometry();
      particlePositions = new Float32Array(maxParticleCount * 3);

      for (let i = 0; i < maxParticleCount; i++) {
        const x = Math.random() * r - rHalf;
        const y = Math.random() * r - rHalf;
        const z = Math.random() * r - rHalf;

        particlePositions[i * 3] = x;
        particlePositions[i * 3 + 1] = y;
        particlePositions[i * 3 + 2] = z;

        particlesData.push({
          velocity: new THREE.Vector3(
            -1 + Math.random() * 2,
            -1 + Math.random() * 2,
            -1 + Math.random() * 2
          ),
          numConnections: 0,
        });
      }

      particles.setDrawRange(0, effectController.current.particleCount);
      particles.setAttribute(
        "position",
        new THREE.BufferAttribute(particlePositions, 3).setUsage(
          THREE.DynamicDrawUsage
        )
      );

      pointCloud = new THREE.Points(particles, pMaterial);
      group.add(pointCloud);

      // Lines setup
      const geometry = new THREE.BufferGeometry();
      geometry.setAttribute(
        "position",
        new THREE.BufferAttribute(positions, 3).setUsage(THREE.DynamicDrawUsage)
      );
      geometry.setAttribute(
        "color",
        new THREE.BufferAttribute(colors, 3).setUsage(THREE.DynamicDrawUsage)
      );
      geometry.setDrawRange(0, 0);

      const material = new THREE.LineBasicMaterial({
        vertexColors: true,
        blending: THREE.AdditiveBlending,
        transparent: true,
      });

      linesMesh = new THREE.LineSegments(geometry, material);
      group.add(linesMesh);

      // Renderer
      renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true, // Enable alpha channel for transparency
      });
      renderer.setPixelRatio(window.devicePixelRatio);
      renderer.setSize(w, h);
      containerRef.current?.appendChild(renderer.domElement);

      // Event listeners
      window.addEventListener("resize", onWindowResize);
    };

    // Animation
    const animate = () => {
      let vertexpos = 0;
      let colorpos = 0;
      let numConnected = 0;

      particlesData.forEach((p) => (p.numConnections = 0));

      for (let i = 0; i < effectController.current.particleCount; i++) {
        const particleData = particlesData[i];
        const i3 = i * 3;

        particlePositions[i3] += particleData.velocity.x;
        particlePositions[i3 + 1] += particleData.velocity.y;
        particlePositions[i3 + 2] += particleData.velocity.z;

        // Boundary checks
        if (
          particlePositions[i3 + 1] < -rHalf ||
          particlePositions[i3 + 1] > rHalf
        )
          particleData.velocity.y *= -1;
        if (particlePositions[i3] < -rHalf || particlePositions[i3] > rHalf)
          particleData.velocity.x *= -1;
        if (
          particlePositions[i3 + 2] < -rHalf ||
          particlePositions[i3 + 2] > rHalf
        )
          particleData.velocity.z *= -1;

        // Particle connections
        for (let j = i + 1; j < effectController.current.particleCount; j++) {
          const particleDataB = particlesData[j];
          const j3 = j * 3;

          const dx = particlePositions[i3] - particlePositions[j3];
          const dy = particlePositions[i3 + 1] - particlePositions[j3 + 1];
          const dz = particlePositions[i3 + 2] - particlePositions[j3 + 2];
          const dist = Math.sqrt(dx * dx + dy * dy + dz * dz);

          if (dist < effectController.current.minDistance) {
            particleData.numConnections++;
            particleDataB.numConnections++;

            const alpha = 1.0 - dist / effectController.current.minDistance;

            positions[vertexpos++] = particlePositions[i3];
            positions[vertexpos++] = particlePositions[i3 + 1];
            positions[vertexpos++] = particlePositions[i3 + 2];

            positions[vertexpos++] = particlePositions[j3];
            positions[vertexpos++] = particlePositions[j3 + 1];
            positions[vertexpos++] = particlePositions[j3 + 2];

            colors[colorpos++] = alpha;
            colors[colorpos++] = alpha;
            colors[colorpos++] = alpha;

            colors[colorpos++] = alpha;
            colors[colorpos++] = alpha;
            colors[colorpos++] = alpha;

            numConnected++;
          }
        }
      }

      // Update geometries
      linesMesh.geometry.setDrawRange(0, numConnected * 2);
      (
        linesMesh.geometry.attributes.position as THREE.BufferAttribute
      ).needsUpdate = true;
      (
        linesMesh.geometry.attributes.color as THREE.BufferAttribute
      ).needsUpdate = true;
      (
        pointCloud.geometry.attributes.position as THREE.BufferAttribute
      ).needsUpdate = true;

      render();
    };

    const render = () => {
      const time = Date.now() * 0.001;
      group.rotation.y = time * 0.1;
      renderer?.render(scene, camera);
    };

    const onWindowResize = () => {
      const { w, h } = getContainerSize();
      camera.aspect = w / h;
      camera.updateProjectionMatrix();
      renderer?.setSize(w, h);
    };

    // Initialize and start animation loop
    init();
    renderer?.setAnimationLoop(animate);

    // Cleanup
    const currentContainer = containerRef.current;
    return () => {
      window.removeEventListener("resize", onWindowResize);

      if (renderer) {
        renderer.setAnimationLoop(null);
        currentContainer.removeChild(renderer.domElement);
      }

      // Dispose Three.js objects
      scene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.geometry.dispose();
          if (Array.isArray(child.material)) {
            child.material.forEach((m) => m.dispose());
          } else {
            child.material.dispose();
          }
        }
      });
    };
  }, []);

  return (
    <div className="w-full h-full absolute flex-center" ref={containerRef} />
  );
};

export default ThreeParticles;
