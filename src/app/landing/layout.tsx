import LandingLogoImg from "@/public/<EMAIL>";
import LandingMeshImg from "@/public/<EMAIL>";
import Image from "next/image";
import Link from "next/link";

export default function LandingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="h-screen relative max-h-screen">
      <div className="bg-linear-[181deg,#100137_17%,#6500CA_99%] -z-10 h-full w-full absolute">
        <Image
          alt="landing-mesh"
          src={LandingMeshImg}
          className="blur-[50px] object-top object-cover"
          fill
        />
      </div>

      <div className="flex flex-col h-full p-4 lg:p-10">
        <div className="flex flex-wrap gap-y-6">
          <Image
            alt="landing-logo"
            src={LandingLogoImg}
            className="max-w-[730px] w-full lg:w-1/5 shrink"
          />
          {/* <div className="grow basis-xs">
            <LandingTab />
          </div> */}
        </div>

        <div className="flex-auto">{children}</div>

        <div className="flex flex-wrap justify-center gap-x-8 opacity-50 text-white">
          <span>©Copyright 2025 LUMINIRIS</span>
          <Link href="https://beian.miit.gov.cn/" target="_blank">
            蜀ICP备2023000608号-2
          </Link>
          <Link
            href="https://beian.mps.gov.cn/#/query/webSearch?code=51019002007727"
            target="_blank"
          >
            川公网安备51019002007727号
          </Link>
        </div>
      </div>
    </div>
  );
}
