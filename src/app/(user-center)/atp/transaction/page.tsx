import { Transactions } from "@/app-ui/components/atp/transactions";
import { di } from "@/app-ui/di";

export default async function TransactionPage() {
  const querySvc = await di.getQuerySvc();
  const atpSvc = await di.getATPSvc();
  const session = await di.ensureLogin();
  const prisma = await di.getPrisma();
  await atpSvc.archivePreConsumptionsToAtp(prisma, session);
  const transactions = await querySvc.transactions();

  return <Transactions transactions={transactions} />;
}
