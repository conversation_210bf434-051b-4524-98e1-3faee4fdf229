import { di } from "@/app-ui/di";
import { IrsModelId } from "@/lib/domain/ai/llm/models";
import { DomainError } from "@/lib/domain/common/error";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { NextRequest } from "next/server";
import { EdgeRuntimeRequestOptions } from "@assistant-ui/react-edge";

export type ChatRunConfig = {
  custom?: {
    threadId: string;
    parentId?: string | null;
    modelId: IrsModelId;
  };
};

async function chat(req: NextRequest) {
  const operator = await di.ensureLogin();
  const llmSvc = await di.getLLMSvc();
  const prisma = await di.getPrisma();
  const reqJson = await req.json();
  const {
    messages,
    runConfig: { custom } = {},
    unstable_assistantMessageId: assistantMsgId,
    maxTokens,
    temperature,
  }: EdgeRuntimeRequestOptions & { runConfig?: ChatRunConfig } = reqJson;

  const threadId = custom?.threadId;
  const parentId = custom?.parentId || undefined;
  const modelId = custom?.modelId;

  if (!threadId) {
    throw new DomainError("thread_not_found");
  }

  if (!assistantMsgId) {
    throw new DomainError("arg_parse_failed");
  }

  if (!modelId) {
    throw new DomainError("llm_not_found");
  }

  const chatStreamRes = await llmSvc.chat(prisma, operator, {
    threadId,
    modelId,
    messages: messages as never,
    assistantMsgId: assistantMsgId,
    parentId: parentId,
    abortSignal: req.signal,
    maxTokens,
    temperature,
  });

  return chatStreamRes.toDataStreamResponse({
    sendReasoning: true,
  });
}

export const POST = createUnifiedAPIRouteHandler(chat);
