import { di } from "@/app-ui/di";
import { NextRequest } from "next/server";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  const tusSvc = await di.getTusSvc();
  return tusSvc.tusServer.handleWeb(req);
});
export const PATCH = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  const tusSvc = await di.getTusSvc();
  return tusSvc.tusServer.handleWeb(req);
});
export const POST = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  const tusSvc = await di.getTusSvc();
  return tusSvc.tusServer.handleWeb(req);
});
export const DELETE = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  const tusSvc = await di.getTusSvc();
  return tusSvc.tusServer.handleWeb(req);
});
export const OPTIONS = createUnifiedAPIRouteHandler(
  async (req: NextRequest) => {
    const tusSvc = await di.getTusSvc();
    return tusSvc.tusServer.handleWeb(req);
  }
);
export const HEAD = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  const tusSvc = await di.getTusSvc();
  return tusSvc.tusServer.handleWeb(req);
});
