import { di } from "@/app-ui/di";
import { RechargeOrderStatus } from "@/lib/domain/atp/m";
import { NextResponse } from "next/server";
import pRetry from "p-retry";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ order_id: string }> }
) {
  const { order_id } = await params;
  const operator = await di.ensureLogin();
  const prisma = await di.getPrisma();
  const atpSvc = await di.getATPSvc();

  const status = await pRetry(
    async (attempCount) => {
      const sync = attempCount > 10;
      const status = await atpSvc.getRechargeStatus(prisma, operator, {
        orderId: order_id,
        sync: sync,
      });
      if (status === RechargeOrderStatus.Pending) {
        throw new Error("status still pending, retry later");
      }

      return status;
    },
    { signal: req.signal, maxTimeout: 3000, retries: 30 }
  );

  return NextResponse.json({ status: status });
}
