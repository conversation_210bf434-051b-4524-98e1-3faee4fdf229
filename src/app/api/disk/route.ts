import { createUnifiedAP<PERSON>outeHand<PERSON> } from "@/lib/infra/nextjs/router-handler";
import { NextRequest, NextResponse } from "next/server";
import { di } from "@/app-ui/di";
import { ListObjectsInputSchema, ObjectInfo } from "@/lib/domain/disk/svc";

export type ListDiskResItem = ObjectInfo;
export type ListDiskRes = ListDiskResItem[];

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const diskSvc = await di.getDiskSvc();

  const params = req.nextUrl.searchParams;

  const parsedInput = ListObjectsInputSchema.parse({
    projectId: params.get("projectId"),
    prefix: params.get("prefix"),
  });

  const objects = await Array.fromAsync(diskSvc.listFirstLevel(parsedInput));
  return NextResponse.json(objects as ListDiskRes);
});
