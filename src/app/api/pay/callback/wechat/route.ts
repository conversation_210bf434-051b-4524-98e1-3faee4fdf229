import { di } from "@/app-ui/di";
import type { NextRequest } from "next/server";
import pRetry from "p-retry";

export async function POST(req: NextRequest) {
  const data = await req.json();
  try {
    const atpSvc = await di.getATPSvc();
    const prisma = await di.getPrisma();

    await pRetry(
      async () => {
        await atpSvc.processRechageCallback(prisma, data);
      },
      { maxTimeout: 500, minTimeout: 500, retries: 6 }
    );

    return new Response();
  } catch (e) {
    console.error("pay callback error", e, data);
    throw e;
  }
}
