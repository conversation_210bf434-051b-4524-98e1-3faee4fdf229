import { NextResponse, type NextRequest } from "next/server";

export const GET = async (
  req: NextRequest,
  { params }: { params: Promise<{ method: string }> }
) => {
  const { method } = await params;
  switch (method) {
    case "req-header":
      const headers = Array.from(req.headers.entries());
      return NextResponse.json({ headers });
    case "timeout":
      await new Promise((r) => setTimeout(r, 60000));
      return NextResponse.json({ done: req.url });
  }

  return NextResponse.json({});
};
