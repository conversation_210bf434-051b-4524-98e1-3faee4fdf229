import {
  AssistantCloudThreadsCreate<PERSON><PERSON>po<PERSON>,
  AssistantCloudThreadsList<PERSON><PERSON><PERSON>,
  AssistantCloudThreadsListResponse,
} from "@/app/api/v1/AssistantCloudThreads";
import { di } from "@/app-ui/di";
import { threadCreateDtoSchema } from "@/lib/domain/thread/m";
import { NextResponse, type NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const operator = await di.ensureLogin();
  const prisma = await di.getPrisma();

  const { is_archived, limit, after }: AssistantCloudThreadsListQuery =
    Object.fromEntries(req.nextUrl.searchParams);

  const threads = await prisma.thread.findMany({
    where: {
      is_archived: is_archived,
      user_id: operator.id,
    },
    take: limit,
    skip: after ? 1 : 0,
    cursor: after ? { id: after } : undefined,
    orderBy: {
      updated_at: "desc",
    },
  });

  const res: AssistantCloudThreadsListResponse = {
    threads: threads as AssistantCloudThreadsListResponse["threads"],
  };
  return NextResponse.json(res);
}

export async function POST(req: NextRequest) {
  const operator = await di.ensureLogin();
  const reqJson = await req.json();

  // TODO: consider use safe-server-action here to do the validations
  const dto = threadCreateDtoSchema.parse(reqJson);

  const prisma = await di.getPrisma();
  const threadSvc = await di.getThreadSvc();
  const thread = await threadSvc.createThread(prisma, operator, dto);

  const res: AssistantCloudThreadsCreateResponse = {
    thread_id: thread.id,
  };
  return NextResponse.json(res, { status: 201 });
}
