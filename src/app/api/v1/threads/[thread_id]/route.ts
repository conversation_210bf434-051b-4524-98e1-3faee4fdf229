import { di } from "@/app-ui/di";
import type { InputJsonValue } from "@prisma/client/runtime/library";
import { NextResponse, type NextRequest } from "next/server";
import { AssistantCloudThreadsUpdateBody } from "@/app/api/v1/AssistantCloudThreads";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { ChatModelConfig } from "@/lib/domain/thread/m";
import { DomainError } from "@/lib/domain/common/error";

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ thread_id: string }> }
) {
  const operator = await di.ensureLogin();
  const prisma = await di.getPrisma();

  const { thread_id } = await params;
  const body: AssistantCloudThreadsUpdateBody = await req.json();

  await prisma.thread.update({
    where: { id: thread_id, user_id: operator.id },
    data: {
      title: body.title,
      metadata: body.metadata as InputJsonValue,
      is_archived: body.is_archived,
    },
  });

  return NextResponse.json({ success: true });
}

export type GetThreadRes = {
  id: string;
  model_config: ChatModelConfig | null;
};

async function getThreadInfo(
  req: NextRequest,
  { params }: { params: Promise<{ thread_id: string }> }
) {
  const { thread_id } = await params;
  const operator = await di.ensureLogin();
  const prisma = await di.getPrisma();

  const thread = await prisma.thread.findUnique({
    where: {
      id: thread_id,
      user_id: operator.id,
    },
    select: {
      id: true,
      model_config: true,
    },
  });

  if (!thread) {
    throw new DomainError("thread_not_found");
  }

  return NextResponse.json(thread as GetThreadRes);
}

export const GET = createUnifiedAPIRouteHandler(getThreadInfo);
