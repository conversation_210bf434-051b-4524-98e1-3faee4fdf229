import { di } from "@/app-ui/di";
import { DomainError } from "@/lib/domain/common/error";
import { NextResponse, type NextRequest } from "next/server";
import { AssistantCloudThreadMessageListResponse } from "@/app/api/v1/AssistantCloudThreadMessages";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ thread_id: string }> }
) {
  const { thread_id } = await params;
  const operator = await di.ensureLogin();
  const prisma = await di.getPrisma();

  const thread = await prisma.thread.findFirst({
    where: {
      id: thread_id,
      user_id: operator.id,
    },
    include: {
      messages: {
        orderBy: {
          created_at: "desc",
        },
      },
    },
  });

  if (!thread) {
    throw new DomainError("thread_not_found");
  }

  const res: AssistantCloudThreadMessageListResponse = {
    messages:
      thread.messages as AssistantCloudThreadMessageListResponse["messages"],
  };
  return NextResponse.json(res, {
    status: 200,
  });
}
