import { PROMPT_TITLE_GENERATOR } from "@/lib/domain/common/constants";
import { di } from "@/app-ui/di";
import { TopicModelId } from "@/lib/domain/ai/llm/models";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { CoreMessage, streamText } from "ai";
import { type NextRequest } from "next/server";
import { IrsModel } from "@/lib/domain/ai/llm/irs";

const FAKE_MSG_ID_FOR_TITLE_GENERATION = "GENERATE_TITLE";

type AssistantCloudRunsStreamBody = {
  thread_id: string;
  assistant_id: "system/thread_title";
  messages: CoreMessage[];
};

function extractTitleFromMsg(msgs: CoreMessage[]) {
  let title = "new chat";

  const userMsg = msgs.find((x) => x.role == "user");
  if (userMsg) {
    if (typeof userMsg.content == "string") {
      title = userMsg.content;
    } else {
      title = userMsg.content
        .filter((x) => x.type == "text")
        .map((x) => x.text)
        .join();
    }
  }

  return title;
}

function extractMessageText(msg: CoreMessage) {
  if (typeof msg.content == "string") {
    return msg.content;
  }

  return msg.content
    .filter((x) => x.type == "text")
    .map((x) => x.text)
    .join("");
}

async function generateTitle(req: NextRequest) {
  const { messages, thread_id }: AssistantCloudRunsStreamBody =
    await req.json();

  const llmPvdSvc = await di.getLLMPvdSvc();
  const thread_svc = await di.getThreadSvc();
  const prisma = await di.getPrisma();
  const atpSvc = await di.getATPSvc();

  const operator = await di.ensureLogin();

  await atpSvc.ensureWithAtp(prisma, operator);

  const title = extractTitleFromMsg(messages);

  await thread_svc.updateThreadTitle(
    prisma,
    {
      threadId: thread_id,
      title: title,
    },
    operator
  );

  const irsLLM = new IrsModel(llmPvdSvc, TopicModelId, { user: operator.id });

  const ac = new AbortController();

  let chunkTexts = "";
  const result = streamText({
    model: irsLLM,
    messages: [
      {
        role: "system",
        content: PROMPT_TITLE_GENERATOR,
      },
      {
        role: "user",
        content: `
        ${messages
          .map((message) => `${message.role}: ${extractMessageText(message)}`)
          .join("\n")}
        
          请总结上述对话为32个字以内的标题。`,
      },
    ],
    onChunk({ chunk }) {
      if (!ac.signal.aborted && chunk.type == "text-delta") {
        chunkTexts += chunk.textDelta;
        if (chunkTexts.length > 32) {
          // 只是为了意外情况下前端不一直 streaming title，正常不该触发
          ac.abort();
        }
      }
    },
    onFinish: async ({ text, usage: tokenUsage }) => {
      if (!ac.signal.aborted) {
        await thread_svc.updateThreadTitle(
          prisma,
          {
            threadId: thread_id,
            title: text,
          },
          operator
        );

        await atpSvc.consumeTokens(prisma, operator, {
          threadId: thread_id,
          msgId: FAKE_MSG_ID_FOR_TITLE_GENERATION,
          irsModelInfo: irsLLM.info,
          tokenUsage: tokenUsage,
        });
      }
    },
    abortSignal: ac.signal,
  });

  // 只是为了意外情况下前端不一直 streaming title，正常不该触发
  ac.signal.onabort = async () => {
    await thread_svc.updateThreadTitle(
      prisma,
      {
        threadId: thread_id,
        title: chunkTexts,
      },
      operator
    );
  };

  return result.toTextStreamResponse();
}

export const POST = createUnifiedAPIRouteHandler(generateTitle);
