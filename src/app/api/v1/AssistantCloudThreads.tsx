export type AssistantCloudThreadsListQuery = {
  is_archived?: boolean;
  limit?: number;
  after?: string;
};

export type CloudThread = {
  id: string;
  user_id: string;
  title: string;
  last_message_at: Date;
  metadata: unknown;
  created_at: Date;
  updated_at: Date;
  is_archived: boolean;
};

export type AssistantCloudThreadsListResponse = {
  threads: CloudThread[];
};

export type AssistantCloudThreadsCreateResponse = {
  thread_id: string;
};

export type AssistantCloudThreadsUpdateBody = {
  title?: string | undefined;
  last_message_at?: Date | undefined;
  metadata?: unknown | undefined;
  is_archived?: boolean | undefined;
};
