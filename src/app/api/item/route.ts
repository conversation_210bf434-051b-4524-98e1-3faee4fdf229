import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { di } from "@/app-ui/di";
import { NextRequest, NextResponse } from "next/server";
import {
  ListItemsByLevelInputSchema,
  ListItemsByLevelRes,
} from "@/lib/query/svc";

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const querySvc = await di.getQuerySvc();

  const params = req.nextUrl.searchParams;

  const parsedInput = ListItemsByLevelInputSchema.parse({
    projectId: params.get("projectId"),
    pid: params.get("pid") ?? undefined,
  });

  const items = await querySvc.listItemsByLevel(parsedInput);
  return NextResponse.json(items as ListItemsByLevelRes[]);
});
