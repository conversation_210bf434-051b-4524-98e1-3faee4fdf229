"use client";

import { createOrder } from "@/app/_actions/safe-actions";
import { App, Button, Checkbox, Modal, Popover, QRCode } from "antd";
import { useEffect, useState } from "react";
import {
  AiFillWechat,
  AiOutlineExclamationCircle,
  AiOutlineLoading3Quarters,
  AiFillCheckCircle,
  AiOutlineQuestionCircle,
} from "react-icons/ai";
import clsx from "clsx";
import { AtpPerFen, RechargeOrderStatus } from "@/lib/domain/atp/m";
import Big from "big.js";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { useVerifyIdModal } from "@/app/_components/account/verify-id-btn";

async function getStatus(orderId: string) {
  const res = await fetch(`/api/recharge/${orderId}/status`);
  if (res.status === 200) {
    const { status } = await res.json();
    return status as RechargeOrderStatus;
  }

  throw new Error("get status failed with: " + res.status);
}

export default function ReCharge({
  idVerifiedPms,
}: {
  idVerifiedPms: Promise<boolean>;
}) {
  const initOpens = {
    confirm: false,
    qrCode: false,
  };
  const router = useRouter();
  const { modal } = App.useApp();

  const [agreed, setAgreed] = useState(false);
  const [opens, setOpens] = useState(initOpens);

  const [rechargeAmount, setRechargeAmount] = useState(1000);
  const [status, setStatus] = useState<RechargeOrderStatus>(
    RechargeOrderStatus.Pending
  );

  const { openVerifyModal } = useVerifyIdModal();

  const atpToBuy = Big(rechargeAmount).times(AtpPerFen).toNumber();

  const rechargeAmountList = [1000, 5000, 10000, 100000, 500000, 1000000];
  const {
    execute: doCreateOrder,
    result: orderRes,
    isPending,
  } = useAction(createOrder);

  const pay_url = isPending ? null : orderRes.data?.pay_code_url;

  useEffect(() => {
    // use isPending to check the latest order
    const orderId = orderRes.data?.order_id;
    if (orderId && !isPending) {
      getStatus(orderId).then((status) => {
        setStatus(status);

        if (isCompleted(status)) {
          router.refresh();
        }
      });
    }
  }, [orderRes, router, isPending]);

  const isCompleted = (status: RechargeOrderStatus) =>
    [RechargeOrderStatus.PaidSuccess, RechargeOrderStatus.Recharged].includes(
      status
    );

  const pay = async () => {
    const isIdVerified = await idVerifiedPms;
    if (isIdVerified) {
      toggleOpen("qrCode", true);
      setStatus(RechargeOrderStatus.Pending);
      doCreateOrder({
        amountByFen: rechargeAmount,
      });
    } else {
      modal.confirm({
        title: "实名认证提示",
        content: "根据相关法规，购买ATP前须完成实名认证。",
        okText: "认证",
        onOk: () => {
          openVerifyModal();
        },
      });
    }
  };

  const onComplete = () => {
    toggleOpen("qrCode", false);
  };

  const onPayClick = async () => {
    if (agreed) {
      await pay();
    } else {
      toggleOpen("confirm", true);
    }
  };

  const onPayCancel = () => {
    toggleOpen("qrCode", false);
  };

  const toggleOpen = (key: keyof typeof opens, open: boolean) => {
    setOpens((prev) => ({ ...prev, [key]: open }));
  };

  const onAgree = async () => {
    setAgreed(true);
    toggleOpen("confirm", false);
    await pay();
  };

  const protocolLink = (
    <a target="_blank" href="/protocol/atp" className="text-[#1677FF]">
      《用户购买ATP协议》
    </a>
  );

  return (
    <>
      <div className="flex gap-2 items-center">
        <div className="text-base font-semibold">购买 ATP</div>
        <Popover
          placement="bottomLeft"
          rootClassName="max-w-[286px]"
          content={
            <div>
              <p>
                “ATP”是平台向您提供的、仅限于在平台内进行相关消费的虚拟工具。
                您购买“ATP”后，可以按照平台相关页面的说明及指引使用、获得平台产品或服务。
                为免生疑义，ATP服务是用户付费、平台向用户持续提供的整体网络技术及相关服务，ATP服务并非网络支付服务，“ATP”也不是代币票券、虚拟货币或预付款凭证，不具备预付价值。
              </p>
            </div>
          }
        >
          <AiOutlineQuestionCircle size={16} />
        </Popover>
      </div>

      <div className="flex gap-2">
        <div className="shrink-0 mr-8 mt-[13px]">购买金额</div>
        <div className="flex-1 grid grid-cols-2 md:grid-cols-3 gap-4">
          {rechargeAmountList.map((amount) => (
            <div
              key={amount}
              className={clsx(
                "min-h-12 border border-[#D8D8D8] rounded-lg px-4 py-3 cursor-pointer font-medium text-[#FFA940]",
                "flex-center transition text-center",
                {
                  "border-irs-primary shadow-[0_0_0_1px] shadow-irs-primary":
                    rechargeAmount === amount,
                }
              )}
              onClick={() => setRechargeAmount(amount)}
            >
              {Big(amount).div(100).toNumber()}元
            </div>
          ))}
        </div>
      </div>
      <div className="flex gap-2">
        <div className="shrink-0 mr-8">ATP 数量</div>
        <div className="font-medium text-black">{atpToBuy}</div>
      </div>
      <div className="flex gap-2">
        <div className="shrink-0 mr-8 mt-[13px]">支付方式</div>
        <div className="w-32 h-12 border-2 border-irs-primary flex-center rounded-lg gap-2">
          <AiFillWechat size={20} className="text-[#52C41A]" />
          微信
        </div>
      </div>
      <div className="flex flex-col gap-4 items-center">
        <Button
          className="w-full h-10 rounded-lg"
          type="primary"
          onClick={onPayClick}
        >
          确认支付
        </Button>
        <div>
          <Checkbox
            checked={agreed}
            onChange={(e) => setAgreed(e.target.checked)}
          >
            我已阅读并同意
            {protocolLink}
          </Checkbox>
        </div>
      </div>

      <Modal
        open={opens.confirm}
        onCancel={() => toggleOpen("confirm", false)}
        onOk={onAgree}
        closable={false}
        cancelText="取消"
        okText="同意并支付"
        width="fit-content"
      >
        <div className="flex gap-4 w-84">
          <AiOutlineExclamationCircle size={24} className="text-[#FAAD14]" />
          <div>
            <h1 className="leading-6 font-semibold">购买ATP协议阅读提醒</h1>
            <p className="my-2 text-[#3D3D3D] leading-[22px]">
              为了更好的保障您的合法权益，
              <br />
              请您阅读并同意以下协议{protocolLink}
            </p>
          </div>
        </div>
      </Modal>

      <Modal
        title="微信扫码支付"
        open={opens.qrCode}
        onCancel={onPayCancel}
        maskClosable={false}
        footer={null}
      >
        <div className="flex flex-col items-center ">
          {isCompleted(status) ? (
            <>
              <AiFillCheckCircle size={72} className="text-[#52C41A]" />
              <h1 className="text-2xl font-medium mt-6">订单支付成功</h1>
              <p className="mt-1 leading-6 text-[#8C8C8C] text-center">
                您可在「账户总览」中查看账户余额，在「订单列表」中查看订单信息。若您有任何问题，请及时咨询客服。
              </p>
              <Button type="primary" className="mt-6" onClick={onComplete}>
                完成
              </Button>
            </>
          ) : (
            <div className="relative w-40 h-40 my-4">
              {pay_url && <QRCode value={pay_url} errorLevel="H" />}
              {!pay_url && (
                <div className="absolute bg-[#1E1E1E] inset-0 rounded-lg flex-center flex-col text-white">
                  <>
                    <AiOutlineLoading3Quarters className="animate-spin" />
                    <span className="mt-2">加载中，请稍等…</span>
                  </>
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </>
  );
}
