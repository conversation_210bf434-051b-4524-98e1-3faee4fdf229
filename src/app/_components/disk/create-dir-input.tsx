import { App, Form, Input } from "antd";
import { DiskTreeId, useUpdateTreeData } from "@/lib/app-store/disk-slice";
import { createDiskDir } from "@/app/_actions/safe-actions/disk";
import {
  DiskDirNameMessage,
  DiskDirNameReg,
} from "@/lib/domain/disk/constants";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";

export type DiskCreateFolderInputProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

type FieldsType = {
  name: string;
};

export function DiskCreateDirInput({
  diskPath,
  projectId,
  treeId,
}: DiskCreateFolderInputProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<FieldsType>();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const parentPath = getParentFolderDiskPath(diskPath);

  const refreshFolder = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [parentPath],
      }),
    });
  };

  const doSubmit = async ({ name }: FieldsType) => {
    await createDiskDir({
      name,
      projectId,
      parentPath,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("文件夹创建成功");
    refreshFolder();
  };

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();

    if (value === "") {
      refreshFolder();
      return;
    }

    try {
      await form.validateFields();
      form.submit();
    } catch {
      refreshFolder();
    }
  };

  return (
    <Form<FieldsType> onFinish={doSubmit} form={form}>
      <Form.Item
        name="name"
        rules={[
          {
            required: true,
            pattern: DiskDirNameReg,
            message: DiskDirNameMessage,
          },
        ]}
        className="mb-0"
      >
        <Input
          autoFocus
          onClick={(e) => e.stopPropagation()}
          size="small"
          onBlur={handleBlur}
        />
      </Form.Item>
    </Form>
  );
}
