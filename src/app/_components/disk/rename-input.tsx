import {
  type DiskTreeId,
  useUpdateTreeData,
  getNameFromDiskPath,
  diskSlice,
  getOriginNameFromDiskPath,
} from "@/lib/app-store/disk-slice";
import { useAppDispatch } from "@/lib/app-store/store";
import {
  DiskDirNameMessage,
  DiskDirNameReg,
  DiskObjectNameMessage,
  DiskObjectNameReg,
} from "@/lib/domain/disk/constants";
import { renameDiskObject } from "@/app/_actions/safe-actions/disk";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { App, Form, Input } from "antd";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";

type FieldsType = {
  name: string;
};

export type DiskRenameInputProps = {
  projectId: string;
  treeId: DiskTreeId;
  diskPath: string;
  isFile?: boolean;
};

export function DiskRenameInput({
  projectId,
  treeId,
  diskPath,
  isFile,
}: DiskRenameInputProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<FieldsType>();

  const dispatch = useAppDispatch();
  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const initName = getNameFromDiskPath(diskPath);
  const parentPath = getParentFolderDiskPath(diskPath);

  const cancelRename = () => {
    dispatch(
      diskSlice.actions.setRenamingNodeKey({
        treeId,
        renamingNodeKey: undefined,
      })
    );
  };

  const refreshFolder = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [parentPath],
      }),
    });
    cancelRename();
  };

  const doSubmit = async ({ name }: FieldsType) => {
    if (name === initName) {
      cancelRename();
      return;
    }

    await renameDiskObject({
      projectId,
      parentPath,
      originName: getOriginNameFromDiskPath(diskPath),
      name,
      isFile,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("重命名成功");
    refreshFolder();
  };

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();

    if (value === "") {
      cancelRename();
      return;
    }

    try {
      await form.validateFields();
      form.submit();
    } catch {
      cancelRename();
    }
  };

  return (
    <Form<FieldsType>
      initialValues={{ name: initName }}
      onFinish={doSubmit}
      form={form}
    >
      <Form.Item
        name="name"
        rules={[
          {
            required: true,
            pattern: isFile ? DiskObjectNameReg : DiskDirNameReg,
            message: isFile ? DiskObjectNameMessage : DiskDirNameMessage,
          },
        ]}
        className="mb-0"
      >
        <Input
          autoFocus
          onClick={(e) => e.stopPropagation()}
          onBlur={handleBlur}
          size="small"
        />
      </Form.Item>
    </Form>
  );
}
