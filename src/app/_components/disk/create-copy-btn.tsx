import { App } from "antd";
import { DiskTreeId, useUpdateTreeData } from "@/lib/app-store/disk-slice";
import { createDiskObjectCopy } from "@/app/_actions/safe-actions/disk";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";

export type DiskCreateCopyBtnProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskCreateCopyBtn({
  diskPath,
  projectId,
  treeId,
}: DiskCreateCopyBtnProps) {
  const { message } = App.useApp();
  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const doCopyDiskObject = async () => {
    await createDiskObjectCopy({
      projectId,
      sourcePath: diskPath,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("副本创建成功");
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [getParentFolderDiskPath(diskPath)],
      }),
    });
  };
  return <div onClick={doCopyDiskObject}>创建副本</div>;
}
