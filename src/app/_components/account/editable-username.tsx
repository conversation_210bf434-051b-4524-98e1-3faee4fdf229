"use client";

import { Button, Form, type FormInstance, Input } from "antd";
import { useEffect, useState } from "react";
import Text from "antd/es/typography/Text";
import { useOptimisticAction } from "next-safe-action/hooks";
import { updateUsername } from "@/app/_actions/safe-actions";
import {
  UsernameReg,
  UserNameMaxByteLength,
} from "@/lib/domain/user/constants";
import { getByteLength } from "@/lib/utils";
import { useRouter } from "next/navigation";

type EditableUsernameProps = {
  initialName: string;
};

type EditableUsernameFormProps = {
  onFinish: (values: { username: string }) => void;
  form: FormInstance;
  initialName: string;
  hasExist: boolean;
};

type UsernameFormValues = {
  username: string;
};

type UsernameActionsProps = {
  isEditing: boolean;
  isUpdatePending: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSubmit: () => void;
};

function EditableUsernameForm({
  onFinish,
  form,
  initialName,
  hasExist,
}: EditableUsernameFormProps) {
  useEffect(() => {
    if (hasExist) {
      form.setFields([
        {
          name: "username",
          errors: ["用户名已存在"],
        },
      ]);
    }
  }, [form, hasExist]);

  const rules = [
    {
      required: true,
      pattern: UsernameReg,
      message: "请输入中文、英文、_、- 或数字",
    },
    {
      validator(_: unknown, value: string) {
        return getByteLength(value) <= UserNameMaxByteLength
          ? Promise.resolve()
          : Promise.reject(new Error("用户名过长"));
      },
    },
  ];
  return (
    <Form
      form={form}
      onFinish={onFinish}
      layout="inline"
      className="[&_.ant-form-item-additional]:absolute [&_.ant-form-item-explain-error]:whitespace-nowrap"
      initialValues={{ username: initialName }}
    >
      <Form.Item name="username" validateFirst rules={rules} className="w-full">
        <Input autoFocus />
      </Form.Item>
    </Form>
  );
}

function UsernameActions({
  isEditing,
  isUpdatePending,
  onEdit,
  onCancel,
  onSubmit,
}: UsernameActionsProps) {
  return isEditing ? (
    <>
      <Button color="primary" variant="link" onClick={onCancel}>
        取消
      </Button>
      <Button color="primary" variant="link" onClick={onSubmit} className="p-0">
        保存
      </Button>
    </>
  ) : (
    <Button
      color="primary"
      variant="link"
      onClick={onEdit}
      disabled={isUpdatePending}
    >
      编辑
    </Button>
  );
}

export function EditableUsername({ initialName }: EditableUsernameProps) {
  const [form] = Form.useForm();
  const router = useRouter();

  const [isEditing, setIsEditing] = useState(false);

  const {
    execute: doUpdateUsername,
    isPending: isUpdatePending,
    result: updateResult,
    hasErrored: hasErroredUpdate,
    optimisticState,
  } = useOptimisticAction(updateUsername, {
    currentState: {
      name: initialName,
      isEditing,
    },
    updateFn: (state, input) => {
      return {
        name: input.username,
        isEditing: false,
      };
    },
    onSuccess: () => {
      setIsEditing(false);
      router.refresh();
    },
  });

  const { name: optimisticName, isEditing: optimisticEditing } =
    optimisticState;

  const hasExist =
    hasErroredUpdate &&
    updateResult.serverError?.error_code === "username_already_exists";

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: UsernameFormValues) => {
    const { username } = values;
    doUpdateUsername({ username });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    form.resetFields();
  };

  return (
    <>
      <div className="flex-1 relative max-w-50 ml-3 text-sm text-[#1E1E1E]">
        {optimisticEditing ? (
          <EditableUsernameForm
            form={form}
            onFinish={handleFinish}
            initialName={initialName}
            hasExist={hasExist}
          />
        ) : (
          <Text ellipsis={{ tooltip: true }}>{optimisticName}</Text>
        )}
      </div>

      <UsernameActions
        isEditing={optimisticEditing}
        isUpdatePending={isUpdatePending}
        onEdit={handleEdit}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
      />
    </>
  );
}
