import { deleteItem } from "@/app/_actions/safe-actions/item";
import { type ItemTreeId, useUpdateTreeData } from "@/lib/app-store/item-slice";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { App } from "antd";

export type ItemDeleteBtnProps = {
  id: string;
  projectId: string;
  treeId: ItemTreeId;
  pid: string;
};

export function ItemDeleteBtn({
  id,
  projectId,
  treeId,
  pid,
}: ItemDeleteBtnProps) {
  const { message, modal } = App.useApp();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const handleDelete = async () => {
    await deleteItem({ itemId: id })
      .then(throwIfSafeActionError)
      .catch(handleApiError);

    message.success("删除成功");
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({ refreshKeys: [pid] }),
    });
  };

  const handleConfirm = () => {
    modal.confirm({
      title: "确认删除",
      content: "确认要删除所选的文件吗？",
      onOk: handleDelete,
    });
  };

  return <div onClick={handleConfirm}>删除</div>;
}
