import { copyItem } from "@/app/_actions/safe-actions/item";
import { useShowItemTransfer } from "@/app/_components/item/show-transfer";
import { ItemTreeId, useUpdateTreeData } from "@/lib/app-store/item-slice";
import { ItemTreeNode } from "@/lib/app-store/item-slice";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { App } from "antd";

export type ItemCopyBtnProps = {
  projectId: string;
  treeId: ItemTreeId;
  node: ItemTreeNode;
};

export function ItemCopyBtn({ projectId, treeId, node }: ItemCopyBtnProps) {
  const { message } = App.useApp();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const showTransfer = useShowItemTransfer({
    title: "复制到",
    projectId,
    doSubmit: async ({ destinationNode }) => {
      await copyItem({
        projectId,
        sourceId: node.key,
        targetId: destinationNode?.key,
      })
        .then(throwIfSafeActionError)
        .catch(handleApiError);
      message.success("复制成功");
      updateTreeData({
        projectId,
        treeId,
        generateUpdateKeys(expandedKeys) {
          return {
            expandedKeys: [...expandedKeys, destinationNode?.key || projectId],
            refreshKeys: [destinationNode?.key || projectId],
          };
        },
      });
    },
  });

  return <div onClick={() => showTransfer()}>复制到</div>;
}
