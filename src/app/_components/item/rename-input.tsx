import { updateItemName } from "@/app/_actions/safe-actions/item";
import {
  itemSlice,
  useUpdateTreeData,
  type ItemTreeId,
} from "@/lib/app-store/item-slice";
import { useAppDispatch } from "@/lib/app-store/store";
import {
  ItemFolderNameReg,
  ItemFolderNameMessage,
  ItemFileNameReg,
  ItemFileNameMessage,
} from "@/lib/domain/item/constants";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { App, Form, Input } from "antd";

type FieldsType = {
  name: string;
};

export type ItemRenameInputProps = {
  projectId: string;
  treeId: ItemTreeId;
  id: string;
  pid: string;
  name: string;
  isFile?: boolean;
};

export function ItemRenameInput({
  projectId,
  treeId,
  id,
  pid,
  name: initName,
  isFile,
}: ItemRenameInputProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<FieldsType>();

  const dispatch = useAppDispatch();
  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const cancelRename = () => {
    dispatch(
      itemSlice.actions.setRenamingNodeKey({
        treeId,
        renamingNodeKey: undefined,
      })
    );
  };

  const refreshFolder = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [pid],
      }),
    });
    cancelRename();
  };

  const doSubmit = async ({ name }: FieldsType) => {
    if (name === initName) {
      cancelRename();
      return;
    }

    await updateItemName({
      itemId: id,
      name,
      isFile,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);

    message.success("重命名成功");
    refreshFolder();
  };

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();

    if (value === "") {
      cancelRename();
      return;
    }

    try {
      await form.validateFields();
      form.submit();
    } catch {
      cancelRename();
    }
  };

  return (
    <Form<FieldsType>
      initialValues={{ name: initName }}
      onFinish={doSubmit}
      form={form}
    >
      <Form.Item
        name="name"
        rules={[
          {
            required: true,
            pattern: isFile ? ItemFileNameReg : ItemFolderNameReg,
            message: isFile ? ItemFileNameMessage : ItemFolderNameMessage,
          },
        ]}
        className="mb-0"
      >
        <Input
          autoFocus
          onClick={(e) => e.stopPropagation()}
          onBlur={handleBlur}
          size="small"
        />
      </Form.Item>
    </Form>
  );
}
