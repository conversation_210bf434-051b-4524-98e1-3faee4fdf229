"use client";

import { AntdRegistry } from "@ant-design/nextjs-registry";
import { ConfigProvider, App } from "antd";
import zhCN from "antd/locale/zh_CN";
import "@ant-design/v5-patch-for-react-19";
import "dayjs/locale/zh-cn";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import StoreProvider from "@/lib/app-store/store-provider";
import { NavigationGuardProvider } from "next-navigation-guard";

dayjs.extend(isBetween);

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AntdRegistry layer>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: "#655DFF",
            colorText: "rgba(23, 31, 43, 0.8)",
          },
          components: {
            Menu: {
              itemSelectedBg: "#F5F5F5",
              itemHeight: 46,
              iconMarginInlineEnd: 8,
            },
            Tree: {
              directoryNodeSelectedBg: "#e7e5ff",
              directoryNodeSelectedColor: "rgba(23, 31, 43, 0.8)",
            },
          },
        }}
      >
        <StoreProvider>
          <NavigationGuardProvider>
            <App component={false}>{children}</App>
          </NavigationGuardProvider>
        </StoreProvider>
      </ConfigProvider>
    </AntdRegistry>
  );
}
