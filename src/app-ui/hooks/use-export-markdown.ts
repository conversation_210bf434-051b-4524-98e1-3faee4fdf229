import { useCallback, useState } from "react";
import dayjs from "dayjs";
import type {
  ThreadMessage,
  ThreadUserContentPart,
  ThreadAssistantContentPart,
} from "@assistant-ui/react";
import { message } from "antd";

const formatUserMessage = (
  content: readonly ThreadUserContentPart[]
): string => {
  return content
    .filter((c) => c.type === "text")
    .map((c) => `## ${c.text}`)
    .join("\n\n");
};

const formatAssistantMessage = (
  content: readonly ThreadAssistantContentPart[]
): string => {
  return content
    .map((c) => {
      switch (c.type) {
        case "text":
          return c.text;
        case "reasoning":
          return c.text;
        default:
          return "";
      }
    })
    .join("\n\n");
};

const formatMessages = (messages: readonly ThreadMessage[]): string => {
  return messages
    .map(({ role, content }) => {
      switch (role) {
        case "user":
          return formatUserMessage(content);
        case "assistant":
          return formatAssistantMessage(content);
        default:
          return "";
      }
    })
    .join("\n\n");
};

export const exportFile = (content: string, filename: string) => {
  const blob = new Blob([content], { type: "plain/text" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");

  a.href = url;
  a.download = filename;

  document.body.append(a);

  a.click();
  URL.revokeObjectURL(url);
  a.remove();
};

export const useExportMarkdown = () => {
  const [markdownExporting, setMarkdownExporting] = useState(false);

  const exportMarkdown = useCallback(
    async ({
      messages,
      title = "share",
    }: {
      messages: readonly ThreadMessage[];
      title?: string;
    }) => {
      if (!markdownExporting) {
        setMarkdownExporting(true);
        const text = formatMessages(messages);
        try {
          exportFile(
            text,
            `IRRISS_AI_${title}_${dayjs().format("YYYY-MM-DD")}.md`
          );
        } catch (error) {
          let errMsg = "导出Markdown文本失败，请稍后重试";
          if (error instanceof Error) {
            errMsg = error.message;
          }
          message.error(errMsg);
        } finally {
          setMarkdownExporting(false);
        }
      }
    },
    [markdownExporting]
  );

  return {
    markdownExporting,
    exportMarkdown,
  };
};
