import dayjs from "dayjs";
import { useCallback, useState } from "react";
import { message } from "antd";
import { snapdom } from "@zumer/snapdom";

export enum ImageType {
  JPG = "jpg",
  PNG = "png",
  SVG = "svg",
  WEBP = "webp",
  JPEG = "jpeg",
}

export const useExportScreenshot = () => {
  const [screenshotExporting, setScreenshotExporting] = useState(false);

  const exportScreenshot = useCallback(
    async ({
      imageType,
      title = "share",
      dom,
    }: {
      dom?: HTMLElement | null;
      imageType: ImageType;
      title?: string;
    }) => {
      if (dom && !screenshotExporting) {
        setScreenshotExporting(true);
        try {
          const result = await snapdom(dom);
          await result.download({
            format: imageType,
            filename: `IRRISS_AI_${title}_${dayjs().format("YYYY-MM-DD")}`,
          });
          setScreenshotExporting(false);
        } catch (error) {
          let errMsg = "导出JPG图片失败，请稍后重试";
          if (error instanceof Error) {
            errMsg = error.message;
          }
          message.error(errMsg);
          setScreenshotExporting(false);
        }
      }
    },
    [screenshotExporting]
  );

  return {
    screenshotExporting,
    exportScreenshot,
  };
};
