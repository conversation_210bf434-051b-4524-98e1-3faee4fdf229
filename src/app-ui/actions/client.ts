import { di } from "@/app-ui/di";
import { handleDomainError } from "@/lib/infra/nextjs/router-handler";
import { createSafeActionClient } from "next-safe-action";

export const actionClient = createSafeActionClient({
  handleServerError(err) {
    const { jsonData } = handleDomainError(err);
    return jsonData;
  },
});

export const authActionClient = actionClient.use(async ({ next }) => {
  const session = await di.ensureLogin();
  return next({ ctx: { session: session } });
});
