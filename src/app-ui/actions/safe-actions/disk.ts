"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import {
  RemoveInputSchema,
  CopyToInputSchema,
  MoveToInputSchema,
  CreateDirInputSchema,
  CreateCopyInputSchema,
  RenameInputSchema,
} from "@/lib/domain/disk/svc";

export const deleteDiskObject = authActionClient
  .inputSchema(RemoveInputSchema)
  .action(
    async ({ parsedInput }) => {
      const diskSvc = await di.getDiskSvc();
      await diskSvc.remove(parsedInput);
    },
    {
      throwServerError: false,
    }
  );

export const copyDiskObject = authActionClient
  .inputSchema(CopyToInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.copyTo(parsedInput);
  });

export const moveDiskObject = authActionClient
  .inputSchema(MoveToInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.moveTo(parsedInput);
  });

export const createDiskDir = authActionClient
  .inputSchema(CreateDirInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.createDir(parsedInput);
  });

export const createDiskObjectCopy = authActionClient
  .inputSchema(CreateCopyInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.createCopy(parsedInput);
  });

export const renameDiskObject = authActionClient
  .inputSchema(RenameInputSchema)
  .action(async ({ parsedInput }) => {
    const diskSvc = await di.getDiskSvc();
    await diskSvc.rename(parsedInput);
  });
