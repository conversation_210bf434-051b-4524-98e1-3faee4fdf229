"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import {
  createProjectInputSchema,
  updateProjectInputSchema,
  deleteProjectInputSchema,
} from "@/lib/domain/project/svc";

export const createProject = authActionClient
  .inputSchema(createProjectInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const projectSvc = await di.getProjectSvc();
    await projectSvc.createProject(prisma, operator, parsedInput);
  });

export const updateProject = authActionClient
  .inputSchema(updateProjectInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const projectSvc = await di.getProjectSvc();
    await projectSvc.updateProject(prisma, operator, parsedInput);
  });

export const deleteProject = authActionClient
  .inputSchema(deleteProjectInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const prisma = await di.getPrisma();
    const projectSvc = await di.getProjectSvc();
    await projectSvc.deleteProject(prisma, operator, parsedInput);
  });
