"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import {
  CreateFolderInputSchema,
  MoveItemInputSchema,
  DeleteItemInputSchema,
  CopyItemInputSchema,
  UpdateItemNameInputSchema,
} from "@/lib/domain/item/svc";

export const createFolder = authActionClient
  .inputSchema(CreateFolderInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const itemSvc = await di.getItemSvc();
    await itemSvc.createFolder(operator, parsedInput);
  });

export const moveItem = authActionClient
  .inputSchema(MoveItemInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const itemSvc = await di.getItemSvc();
    await itemSvc.moveItem(operator, parsedInput);
  });

export const deleteItem = authActionClient
  .inputSchema(DeleteItemInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const itemSvc = await di.getItemSvc();
    await itemSvc.deleteItem(operator, parsedInput);
  });

export const updateItemName = authActionClient
  .inputSchema(UpdateItemNameInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const itemSvc = await di.getItemSvc();
    await itemSvc.updateItemName(operator, parsedInput);
  });

export const copyItem = authActionClient
  .inputSchema(CopyItemInputSchema)
  .action(async () => {
    const itemSvc = await di.getItemSvc();
    await itemSvc.copyItem();
  });
