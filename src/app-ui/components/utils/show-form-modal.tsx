import { useHandleApiErrorDefault } from "@/lib/utils";
import { App, FormProps, ModalFuncProps } from "antd";
import useForm from "antd/es/form/hooks/useForm";

import type { JSX } from "react";

interface Config<FieldType> {
  modelConfig?: ModalFuncProps;
  formProps?: FormProps<FieldType>;
  formFC: (formProps: FormProps<FieldType>) => JSX.Element;
  doSubmit: (formValues: FieldType) => Promise<void>;
  doubleConfirmModelConfig?: ModalFuncProps;
}

export function useShowFormModal<FieldType>({
  modelConfig,
  formProps,
  formFC: AForm,
  doSubmit,
  doubleConfirmModelConfig,
}: Config<FieldType>) {
  const app = App.useApp();
  const [form] = useForm<FieldType>();

  const handleApiErrorDefault = useHandleApiErrorDefault();

  const showModal = (initialValues?: FieldType) => {
    const formInitialValues = initialValues || formProps?.initialValues;

    let firstOnOk = async () => {
      const formValues = await form.validateFields();
      await doSubmit({ ...formInitialValues, ...formValues }).catch(
        handleApiErrorDefault
      );
      form.resetFields();
    };

    if (doubleConfirmModelConfig) {
      firstOnOk = async () => {
        const formValues = await form.validateFields();

        app.modal.confirm({
          footer(_, { OkBtn, CancelBtn }) {
            return (
              <>
                <OkBtn />
                <CancelBtn />
              </>
            );
          },
          async onOk() {
            await doSubmit({ ...formInitialValues, ...formValues }).catch(
              handleApiErrorDefault
            );
            form.resetFields();
          },
          ...doubleConfirmModelConfig,
        });
      };
    }

    return app.modal.confirm({
      icon: <></>,
      width: 520,
      content: (
        <AForm
          preserve={false}
          {...formProps}
          initialValues={formInitialValues}
          form={form}
        />
      ),
      onOk: firstOnOk,
      ...modelConfig,
    });
  };
  return showModal;
}
