"use client";

import { App, But<PERSON> } from "antd";

type IdCardInfo = {
  redacted_id_card_no: string;
  redacted_id_card_name: string;
};

function IdCardDetail({
  redacted_id_card_name,
  redacted_id_card_no,
}: IdCardInfo) {
  return (
    <div className="text-[#1E1E1E] flex flex-col gap-2 my-6">
      <div className="flex gap-2 items-center h-8">
        <div className="flex gap-1">
          <span>真实姓名</span>
          <span>:</span>
        </div>
        <div className="px-3">{redacted_id_card_name}</div>
      </div>
      <div className="flex gap-2 items-center h-8">
        <div className="flex gap-1">
          <span>证件号码</span>
          <span>:</span>
        </div>
        <div className="px-3">{redacted_id_card_no}</div>
      </div>
    </div>
  );
}

export function ShowIdDetailBtn({ idCardInfo }: { idCardInfo: IdCardInfo }) {
  const { modal } = App.useApp();

  const openModal = () => {
    modal.success({
      title: "实名认证信息",
      content: <IdCardDetail {...idCardInfo} />,
      okText: "确认",
      icon: null,
      className: "[&_.ant-modal-confirm-btns]:mt-0",
      closable: true,
    });
  };

  return (
    <Button color="primary" variant="link" onClick={openModal}>
      查看
    </Button>
  );
}
