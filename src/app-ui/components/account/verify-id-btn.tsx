"use client";

import { useRef, useState } from "react";
import { CardNoReg } from "@/lib/domain/user/constants";
import { Button, Form, Input, Checkbox, Flex, FormProps, App } from "antd";
import { useAction } from "next-safe-action/hooks";
import { verifyIdCard } from "@/app-ui/actions/safe-actions";
import { useRouter } from "next/navigation";
import { AiFillCheckCircle } from "react-icons/ai";
import { ModalFunc } from "antd/es/modal/confirm";

function VerifySuccess({ onClose }: { onClose: () => void }) {
  return (
    <div className="my-6 flex flex-col items-center">
      <AiFillCheckCircle size={72} className="text-[#52C41A]" />
      <p className="text-2xl font-medium my-6">认证成功</p>
      <Button htmlType="submit" type="primary" onClick={onClose}>
        确认
      </Button>
    </div>
  );
}

function VerifyForm({
  ref,
  ...rest
}: FormProps & {
  ref: React.RefObject<ReturnType<ModalFunc> | null>;
}) {
  const router = useRouter();
  const [form] = Form.useForm();

  const [agreed, setAgreed] = useState(false);

  const {
    execute: doVerifyIdCard,
    isPending: isVerifyIdCardPending,
    status: verifytIdCardStatus,
  } = useAction(verifyIdCard, {
    onSuccess: () => {
      ref?.current?.update({
        closable: true,
        maskClosable: true,
        content: <VerifySuccess onClose={onClose} />,
      });
      router.refresh();
    },
  });

  const onFinish = (values: { realName: string; cardNo: string }) => {
    ref?.current?.update({
      closable: false,
    });
    doVerifyIdCard(values);
  };

  const onClose = () => {
    ref.current?.destroy();
    form?.resetFields();
  };

  const showError = verifytIdCardStatus === "hasErrored";

  return (
    <>
      <p className="py-2 text-xs leading-5 text-[#8C8C8C]">
        根据相关法律法规，不对未满14周岁的个人提供在线实名认证服务。认证成功后，暂不支持更改个人实名认证，敬请谅解。
      </p>
      {showError && (
        <p className="text-xs leading-5 text-[#FF4D4F]">
          *认证失败，请确认实名信息后重新认证。
        </p>
      )}
      <Form className="mt-6" form={form} onFinish={onFinish} {...rest}>
        <Form.Item
          label="真实姓名"
          name="realName"
          rules={[{ required: true }]}
        >
          <Input placeholder="请输入真实姓名" />
        </Form.Item>

        <Form.Item
          label="证件号码"
          name="cardNo"
          rules={[
            {
              required: true,
              pattern: CardNoReg,
              message: "请输入中国大陆二代居民身份证号码",
            },
          ]}
        >
          <Input placeholder="请输入中国大陆二代居民身份证号码" />
        </Form.Item>

        <Checkbox
          checked={agreed}
          onChange={(e) => setAgreed(e.target.checked)}
          className="text-xs leading-5 text-[#8C8C8C] select-none [&_.ant-checkbox]:self-start [&_.ant-checkbox]:mt-1"
        >
          您理解并同意IRRISS有权自行或委托第三方，审查您在实名认证时提供的信息是否真实、准确及有效。若提供虚假信息，由此带来的后果全部由您承担。
        </Checkbox>
        <Flex gap={8} justify="end" className="mt-2">
          {!isVerifyIdCardPending && <Button onClick={onClose}>取消</Button>}
          <Button
            htmlType="submit"
            type="primary"
            disabled={!agreed || isVerifyIdCardPending}
          >
            {isVerifyIdCardPending ? "认证中..." : "提交"}
          </Button>
        </Flex>
      </Form>
    </>
  );
}

export function useVerifyIdModal() {
  const { modal } = App.useApp();

  const ins = useRef<ReturnType<ModalFunc>>(null);

  const openModal = () => {
    ins.current = modal.confirm({
      title: "实名认证",
      icon: null,
      closable: true,
      footer: null,
      content: <VerifyForm ref={ins} />,
      maskClosable: false,
    });
  };

  return {
    openVerifyModal: openModal,
  };
}

export function VerifyIdBtn() {
  const { openVerifyModal } = useVerifyIdModal();

  return (
    <Button color="primary" variant="link" onClick={openVerifyModal}>
      认证
    </Button>
  );
}
