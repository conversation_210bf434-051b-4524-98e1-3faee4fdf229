import AccountInfo from "@/app-ui/components/account/info";
import { Tabs, type TabsProps } from "antd";
import { Suspense } from "react";
import { SktAccountInfo } from "@/app-ui/components/skeletons";

export default function AccountTabs() {
  const items: TabsProps["items"] = [
    {
      key: "account-info",
      label: "基本信息",
      children: (
        <Suspense fallback={<SktAccountInfo />}>
          <AccountInfo />
        </Suspense>
      ),
    },
  ];

  return <Tabs defaultActiveKey="account-info" items={items} />;
}
