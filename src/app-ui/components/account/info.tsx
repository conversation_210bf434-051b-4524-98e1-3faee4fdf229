import { di } from "@/app-ui/di";
import Text from "antd/es/typography/Text";
import { ShowIdDetailBtn } from "@/app-ui/components/account/show-id-detail-btn";
import { VerifyIdBtn } from "@/app-ui/components/account/verify-id-btn";
import { EditableUsername } from "@/app-ui/components/account/editable-username";
import { AiOutlineCheck } from "react-icons/ai";

export default async function AccountInfo() {
  const user = await di.ensureLogin();
  const prisma = await di.getPrisma();
  const userSvc = await di.getUserSvc();
  const profile = await userSvc.profile(prisma, user);

  const { id, redacted_phone } = user;
  const id_card_verified = !!profile.id_card_verified;

  const data: {
    key: string;
    value?: string;
    label: string;
    button?: React.ReactNode;
    customContent?: React.ReactNode;
  }[] = [
    {
      key: "name",
      label: "用户名",
      customContent: <EditableUsername initialName={profile.name ?? ""} />,
    },
    {
      key: "phone",
      value: redacted_phone,
      label: "手机号",
    },
    {
      key: "id",
      value: id,
      label: "用户ID",
      button: (
        <Text
          copyable={{
            text: id,
            tooltips: false,
            icon: [
              <div key="copy" className="px-[15px]">
                复制
              </div>,
              <div key="check" className="px-[15px]">
                <AiOutlineCheck />
              </div>,
            ],
          }}
          className="[&_.ant-typography-copy]:text-[#7832f1]"
        />
      ),
    },
    {
      key: "wechat",
      value: "已绑定",
      label: "微信",
    },
    {
      key: "auth",
      value: id_card_verified ? "已认证" : "未认证",
      label: "实名",
      button: id_card_verified ? (
        <ShowIdDetailBtn
          idCardInfo={{
            redacted_id_card_no: profile.redacted_id_card_no!,
            redacted_id_card_name: profile.redacted_id_card_name!,
          }}
        />
      ) : (
        <VerifyIdBtn />
      ),
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      {data.map(({ key, value, button, label, customContent }) => (
        <div key={key} className="h-8 flex items-center">
          <div className="flex items-center justify-between flex-row-reverse w-18">
            <span>{label}：</span>
          </div>
          {customContent ?? (
            <>
              <div className="flex-1 max-w-50 ml-3 text-sm text-[#1E1E1E]">
                <Text ellipsis={{ tooltip: true }}>{value || "-"}</Text>
              </div>
              {button}
            </>
          )}
        </div>
      ))}
    </div>
  );
}
