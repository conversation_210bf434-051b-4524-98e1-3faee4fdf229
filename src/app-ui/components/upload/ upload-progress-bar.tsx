"use client";

import { UploadListModal } from "@/app-ui/components/upload/upload-list-modal";
import { getUppy } from "@/lib/uppy";
import { useUppyState } from "@uppy/react";
import { Flex } from "antd";
import { useNavigationGuard } from "next-navigation-guard";
import TransportSvg from "@/public/transport-outlined.svg";
import { useState } from "react";

export function UploadProgressBar() {
  const [uppy] = useState(getUppy);

  const [listOpen, setListOpen] = useState(false);

  const files = useUppyState(uppy, (state) => state.files);
  const isUploading = useUppyState(
    uppy,
    (state) => Object.keys(state.currentUploads).length > 0
  );

  const fileList = Object.values(files);

  useNavigationGuard({
    enabled: isUploading,
  });

  const getStateColor = () => {
    if (isUploading) {
      return "text-[#1677FF]";
    } else if (fileList.length === 0) {
      return "text-black/45";
    } else {
      return "text-[#52C41A]";
    }
  };

  return (
    <Flex justify="end" className="h-5 px-3">
      <Flex
        align="center"
        onClick={() => setListOpen(true)}
        className="cursor-pointer"
        gap={4}
      >
        <TransportSvg className="size-4 text-black/45" />
        <span className={`${getStateColor()} text-xs`}>传输</span>
      </Flex>

      <UploadListModal
        open={listOpen}
        onCancel={() => setListOpen(false)}
        onOk={() => setListOpen(false)}
      />
    </Flex>
  );
}
