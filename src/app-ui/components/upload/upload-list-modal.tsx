"use client";

import { getUppy } from "@/lib/uppy";
import { humanFileSize } from "@/lib/utils";
import type { Meta, UppyFile, Body } from "@uppy/core";
import { useUppyState } from "@uppy/react";
import { Modal, Progress, type ProgressProps, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import { useState } from "react";

type FileType = UppyFile<Meta, Body>;

export type UploadListModalProps = {
  open: boolean;
  onOk: () => void;
  onCancel: () => void;
};

function UploadListTable() {
  const [uppy] = useState(getUppy);
  const files = useUppyState(uppy, (state) => state.files);

  const fileList = Object.values(files);

  const columns: ColumnsType<FileType> = [
    {
      title: "名称",
      dataIndex: ["meta", "relativePath"],
      width: 300,
      ellipsis: true,
    },
    {
      title: "大小",
      dataIndex: "size",
      render: (size) => humanFileSize(size),
    },
    {
      title: "状态",
      key: "status",
      render: (
        _,
        { error, progress: { percentage, uploadComplete, uploadStarted } }
      ) => {
        let status: ProgressProps["status"] = "normal";
        if (error) {
          status = "exception";
        } else if (uploadComplete) {
          status = "success";
        } else if (uploadStarted) {
          status = "active";
        }
        return <Progress percent={percentage} status={status} />;
      },
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={fileList}
      pagination={false}
      rowKey="id"
    />
  );
}

export function UploadListModal({
  open,
  onOk,
  onCancel,
}: UploadListModalProps) {
  return (
    <Modal
      width={720}
      open={open}
      maskClosable={false}
      onOk={onOk}
      onCancel={onCancel}
    >
      <UploadListTable />
    </Modal>
  );
}
