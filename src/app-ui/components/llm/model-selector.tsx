import { Select, type SelectProps } from "antd";
import { IrsModelId, Models } from "@/lib/domain/ai/llm/models";
import { llmSlice } from "@/lib/app-store/llm-slice";
import { diverseApi } from "@/lib/apis/diverse/api";
import { useQueryState } from "nuqs";
import { QueryThreadIdName } from "@/app-ui/components/assistant-ui/thread-list";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { useEffect } from "react";
import { useHandleApiErrorDefault } from "@/lib/utils";

export default function ModelSelector() {
  const [queryThreadId] = useQueryState(QueryThreadIdName);
  const modelId = useAppSelector(llmSlice.selectors.selectModelId);
  const dispatch = useAppDispatch();
  const [getThread] = diverseApi.useLazyGetThreadQuery();
  const handleApiErrorDefault = useHandleApiErrorDefault();

  useEffect(() => {
    if (queryThreadId) {
      getThread(queryThreadId, true)
        .unwrap()
        .then((thread) => {
          if (thread.model_config) {
            dispatch(llmSlice.actions.setModelCfg(thread.model_config));
          } else {
            dispatch(llmSlice.actions.saveCfgToRemote(queryThreadId)).unwrap();
          }
        })
        .catch(handleApiErrorDefault);
    } else {
      dispatch(
        llmSlice.actions.setModelCfg({ modelId: IrsModelId.DeepSeekR1 })
      );
    }
  }, [dispatch, getThread, handleApiErrorDefault, queryThreadId]);

  const options: SelectProps["options"] = Models.map((model) => ({
    label: model.name,
    value: model.id,
  }));

  const handleChange = async (modelId: IrsModelId) => {
    dispatch(llmSlice.actions.setModelCfg({ modelId }));

    if (queryThreadId) {
      // TODO: handle error for next-safe-action res
      await dispatch(llmSlice.actions.saveCfgToRemote(queryThreadId)).unwrap();
    }
  };

  return (
    <Select
      value={modelId}
      onChange={handleChange}
      variant="borderless"
      className="min-w-30 ml-1 [&_.ant-select-selector]:text-white [&_.ant-select-selection-item]:text-white [&_.ant-select-arrow]:text-white"
      popupMatchSelectWidth={false}
      options={options}
    />
  );
}
