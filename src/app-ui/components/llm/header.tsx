import { useCallback } from "react";
import SidebarTrigger from "@/app-ui/components/layout/sidebar-trigger";
import { Icon } from "@/app-ui/components/ui/icon";
import ModelSelector from "@/app-ui/components/llm/model-selector";
import IconSetting from "@/app-ui/components/llm/icon-setting";
import IconExport from "@/app-ui/components/llm/icon-export";
import {
  useThreadListItemRuntime,
  useThreadRuntime,
} from "@assistant-ui/react";
import Logo from "@/app-ui/components/llm/logo";
import ChatRobotSvg from "@/public/chat-robot-outlined.svg";

export type ChatHeaderProps = {
  sideNav: React.ReactNode;
  threadMsgsDomRef: React.RefObject<HTMLDivElement | null>;
};

export default function ChatHeader({
  sideNav,
  threadMsgsDomRef,
}: ChatHeaderProps) {
  const tlir = useThreadListItemRuntime();
  const tr = useThreadRuntime();
  const getExportInfo = useCallback(() => {
    const title = tlir.getState().title;
    const messages = tr.getState().messages;
    return { title, messages };
  }, [tlir, tr]);

  return (
    <div className="h-12 flex items-center justify-between bg-[#272D34] text-white">
      <div className="flex items-center gap-0 md:gap-6">
        <SidebarTrigger>
          <Logo />
          {sideNav}
        </SidebarTrigger>
        <ModelSelector />
      </div>
      <div className="flex items-center gap-2 mr-2">
        <Icon component={<ChatRobotSvg className="size-6" />} />
        <IconSetting />
        <IconExport
          threadMsgsDomRef={threadMsgsDomRef}
          getExportInfo={getExportInfo}
        />
      </div>
    </div>
  );
}
