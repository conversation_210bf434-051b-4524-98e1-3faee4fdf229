import { <PERSON><PERSON>, <PERSON>lex, <PERSON>put<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lider } from "antd";
import { Icon } from "@/app-ui/components/ui/icon";
import { AiOutlineControl, AiOutlineQuestionCircle } from "react-icons/ai";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { llmSlice } from "@/lib/app-store/llm-slice";
import { useQueryState } from "nuqs";
import { QueryThreadIdName } from "@/app-ui/components/assistant-ui/thread-list";

type LabelProps = {
  text: string;
  description: string;
};

function Label({ text, description }: LabelProps) {
  return (
    <div className="w-[140px]">
      <div className="flex items-center text-[#1E1E1E] h-8">
        {text}
        <span className="mx-1">
          <AiOutlineQuestionCircle size={16} className="text-[#8C8C8C]" />
        </span>
        :
      </div>
      <div className="text-[#BFBFBF] h-5 text-xs leading-5">{description}</div>
    </div>
  );
}

type SliderInputFieldProps = {
  min: number;
  max: number;
  value: number;
  step: number;
  onValueChange: (value: number | null) => void;
};

function SliderInputField({
  min,
  max,
  value,
  step,
  onValueChange,
}: SliderInputFieldProps) {
  return (
    <div className="flex-1 flex items-start gap-6">
      <Slider
        min={min}
        max={max}
        step={step}
        onChange={onValueChange}
        className="flex-1"
        value={value}
      />

      <InputNumber
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onValueChange}
      />
    </div>
  );
}

function MaxTokensField() {
  const cfg = useAppSelector(
    (s) => llmSlice.selectors.selectModelCfg(s).maxTokens
  );
  const dispatch = useAppDispatch();
  const setMaxTokens = (v: number | null) => {
    if (v) {
      dispatch(llmSlice.actions.updateMaxTokens(v));
    }
  };

  return <SliderInputField onValueChange={setMaxTokens} {...cfg} />;
}

function TemperatureField() {
  const cfg = useAppSelector(
    (s) => llmSlice.selectors.selectModelCfg(s).temperature
  );
  const dispatch = useAppDispatch();
  const setTemperature = (v: number | null) => {
    if (v) {
      dispatch(llmSlice.actions.updateTemperature(v));
    }
  };
  return <SliderInputField onValueChange={setTemperature} {...cfg} />;
}

function SettingForm() {
  const dispatch = useAppDispatch();
  const onResetClick = () => {
    dispatch(llmSlice.actions.resetModelCfg());
  };

  return (
    <div>
      <header className="px-6 pt-4 pb-2 text-[#1E1E1E] font-semibold text-base">
        基本参数
      </header>
      <div className="p-6 flex flex-col gap-4">
        <Flex>
          <Label text="生成文本长度" description="Max Output Tokens" />
          <MaxTokensField />
        </Flex>
        <Flex>
          <Label text="创意活跃度" description="Temperature" />
          <TemperatureField />
        </Flex>
      </div>

      <div className="px-6 flex justify-end pb-6">
        <Button onClick={onResetClick}>全部重置</Button>
      </div>
    </div>
  );
}

export default function ModelSetting() {
  const dispatch = useAppDispatch();
  const [queryThreadId] = useQueryState(QueryThreadIdName);
  const modelParamsChanged = useAppSelector(
    llmSlice.selectors.selectModelParamsChanged
  );

  const onOpenChange = async (open: boolean) => {
    if (!open && queryThreadId) {
      // TODO: handle error for next-safe-action res
      await dispatch(llmSlice.actions.saveCfgToRemote(queryThreadId)).unwrap();
    }
  };

  return (
    <Popover
      arrow={false}
      trigger="click"
      placement="bottomRight"
      content={<SettingForm />}
      onOpenChange={onOpenChange}
      styles={{
        body: {
          width: 520,
          padding: 0,
        },
      }}
    >
      <div>
        <Icon
          active={modelParamsChanged}
          component={<AiOutlineControl size={24} />}
        />
      </div>
    </Popover>
  );
}
