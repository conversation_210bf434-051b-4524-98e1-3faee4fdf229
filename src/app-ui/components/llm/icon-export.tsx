"use client";

import { useState } from "react";
import { Dropdown, type MenuProps } from "antd";
import { AiOutlineUpload } from "react-icons/ai";
import { Icon } from "@/app-ui/components/ui/icon";
import {
  ExportPictureButton,
  ExportMarkdownButton,
} from "@/app-ui/components/llm/export-btns";
import { type ThreadMessage } from "@assistant-ui/react";

export type IconExportProps = {
  threadMsgsDomRef: React.RefObject<HTMLDivElement | null>;
  getExportInfo(): {
    title?: string;
    messages: readonly ThreadMessage[];
  };
};

export default function IconExport({
  threadMsgsDomRef,
  getExportInfo,
}: IconExportProps) {
  const [open, setOpen] = useState(false);

  const toggleOpen = (nextOpen: boolean) => {
    setOpen(nextOpen);
  };

  const items: MenuProps["items"] = [
    {
      key: "markdown",
      label: <ExportMarkdownButton getExportInfo={getExportInfo} />,
    },
    {
      key: "picture",
      label: (
        <ExportPictureButton
          getExportInfo={getExportInfo}
          threadMsgsDomRef={threadMsgsDomRef}
        />
      ),
    },
  ];

  return (
    <Dropdown menu={{ items }} trigger={["click"]} onOpenChange={toggleOpen}>
      <div>
        <Icon component={<AiOutlineUpload size={24} />} active={open} />
      </div>
    </Dropdown>
  );
}
