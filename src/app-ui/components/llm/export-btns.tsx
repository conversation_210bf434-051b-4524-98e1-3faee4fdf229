"use client";

import Image from "next/image";
import { clsx } from "clsx";
import { ThreadMessage } from "@assistant-ui/react";
import {
  useExportScreenshot,
  ImageType,
} from "@/app-ui/hooks/use-export-screenshot";
import { useExportMarkdown } from "@/app-ui/hooks/use-export-markdown";

export type ExportButtonProps = {
  src: string;
  alt: string;
  desc: string;
  onClick: () => void;
  disabled?: boolean;
};

export type ExportMarkdownButtonProps = {
  getExportInfo(): {
    title?: string;
    messages: readonly ThreadMessage[];
  };
};

export type ExportPictureButtonProps = {
  threadMsgsDomRef: React.RefObject<HTMLDivElement | null>;
  getExportInfo(): { title?: string };
};

export function ExportButton({
  src,
  alt,
  desc,
  onClick,
  disabled,
}: ExportButtonProps) {
  return (
    <div
      className={clsx(
        "flex gap-2 items-center select-none h-8",
        disabled ? "cursor-not-allowed" : "cursor-pointer"
      )}
      {...(!disabled && {
        onClick,
      })}
    >
      <Image src={src} width={16} height={16} alt={alt} priority />
      <span className="text-[#000000e0]">{desc}</span>
    </div>
  );
}

export function ExportMarkdownButton({
  getExportInfo,
}: ExportMarkdownButtonProps) {
  const { exportMarkdown, markdownExporting } = useExportMarkdown();

  const doExportMarkdown = () => {
    const info = getExportInfo();
    exportMarkdown(info);
  };

  return (
    <ExportButton
      src="/markdown-outlined.svg"
      alt="export markdown"
      desc="导出文本(MarkDown)"
      onClick={doExportMarkdown}
      disabled={markdownExporting}
    />
  );
}

export function ExportPictureButton({
  threadMsgsDomRef,
  getExportInfo,
}: ExportPictureButtonProps) {
  const { exportScreenshot, screenshotExporting } = useExportScreenshot();

  const doExportPicture = () => {
    const { title } = getExportInfo();
    exportScreenshot({
      dom: threadMsgsDomRef.current,
      imageType: ImageType.JPG,
      title,
    });
  };

  return (
    <ExportButton
      src="/picture-outlined.svg"
      alt="export picture"
      desc="导出图片(JPG)"
      onClick={doExportPicture}
      disabled={screenshotExporting}
    />
  );
}
