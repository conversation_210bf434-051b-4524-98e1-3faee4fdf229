"use client";

import { Thread } from "@/app-ui/components/assistant-ui/thread";
import { ThreadList } from "@/app-ui/components/assistant-ui/thread-list";
import {
  AssistantCloud,
  AssistantRuntimeProvider,
  type AssistantRuntime,
} from "@assistant-ui/react";
import { useChatRuntime } from "@assistant-ui/react-ai-sdk";
import { Layout } from "antd";
import { Content } from "antd/es/layout/layout";
import Sider from "antd/es/layout/Sider";
import Header from "@/app-ui/components/llm/header";
import Logo from "@/app-ui/components/llm/logo";
import { Suspense, useEffect, useRef } from "react";
import { SessionUser } from "@/lib/domain/user/m";
import { ChatContext } from "@/app-ui/context/chat-context";
import {
  extractApiCodeFromChatError,
  useHandleApiErrorDefault,
  useHandleAtpNotSufficientError,
} from "@/lib/utils";
import { useAppStore } from "@/lib/app-store/store";
import { llmSlice } from "@/lib/app-store/llm-slice";

const hackedEmptyPromise = Promise.resolve({ message_id: "empty" });

type AssistantProps = {
  user: SessionUser;
};

export const Assistant = ({ user }: AssistantProps) => {
  const store = useAppStore();

  const threadMsgsDomRef = useRef<HTMLDivElement>(null);

  const handleApiErrorDefault = useHandleApiErrorDefault();
  const handleAtpNotSufficientError = useHandleAtpNotSufficientError();

  const cloud = new AssistantCloud({
    baseUrl: global.location?.origin + "/api",
    async authToken() {
      // just for hacky, not really auth token
      return "eyJhbGciOiJub25lIn0.eyJleHAiOjIwNTEyMjI0MDB9.";
    },
  });

  cloud.threads.messages.create = () => hackedEmptyPromise;

  const onError = (error: Error) => {
    const error_code = extractApiCodeFromChatError(error);
    if (error_code === "atp_not_sufficient") {
      handleAtpNotSufficientError();
    } else if (error.name === "AbortError") {
      // ignore this error, abort is an normal behavior
    } else {
      handleApiErrorDefault({
        status: 400,
        data: { error_code },
      });
    }
  };

  const runtime: AssistantRuntime = useChatRuntime({
    api: "/api/chat",
    cloud: cloud,
    sendExtraMessageFields: true,
    onError,
  });

  const sideNav = (
    <Suspense>
      <ThreadList />
    </Suspense>
  );

  useEffect(() => {
    const unSub = runtime.registerModelContextProvider({
      getModelContext() {
        const cfg = llmSlice.selectors.selectModelCfg(store.getState());
        return {
          callSettings: {
            maxTokens: cfg.maxTokens.value,
            temperature: cfg.temperature.value,
          },
        };
      },
    });
    return unSub;
  }, [runtime, store]);

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <ChatContext value={{ user }}>
        <Layout className="h-dvh">
          <Layout>
            <Sider
              width={250}
              theme="light"
              collapsedWidth={0}
              breakpoint="md"
              trigger={null}
              className="hidden md:block border-none"
            >
              <Logo />
              {sideNav}
            </Sider>
            <Content>
              <Header sideNav={sideNav} threadMsgsDomRef={threadMsgsDomRef} />
              <Thread threadMsgsDomRef={threadMsgsDomRef} />
            </Content>
          </Layout>
        </Layout>
      </ChatContext>
    </AssistantRuntimeProvider>
  );
};
