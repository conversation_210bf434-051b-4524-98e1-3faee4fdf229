import { Table } from "antd";

export function SktAccountInfo() {
  return (
    <div className="animate-pulse flex flex-col gap-4">
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
      <div className="h-8 w-2xs bg-gray-200 rounded-md" />
    </div>
  );
}

export function SktATPBalance() {
  return <div className="h-9 w-40 bg-gray-200/30 rounded-md animate-pulse" />;
}

export function SktRechargeOrders() {
  const columns = [
    "内容",
    "订单号",
    "支付金额",
    "支付流水号",
    "类型",
    "创建时间",
    "状态",
  ];
  return (
    <Table
      className="[&_.ant-spin-nested-loading]:h-[50vh] [&_.ant-table-content]:overflow-hidden! [&_.ant-table-tbody]:hidden"
      columns={columns.map((text) => ({ title: text }))}
      loading
      locale={{ emptyText: " " }}
      scroll={{ x: "max-content" }}
    />
  );
}

export function SktTransactions() {
  const columns = ["账单号", "内容", "类型", "数量", "时间"];
  return (
    <Table
      className="[&_.ant-spin-nested-loading]:h-[50vh] [&_.ant-table-content]:overflow-hidden! [&_.ant-table-tbody]:hidden"
      columns={columns.map((text) => ({ title: text }))}
      loading
      locale={{ emptyText: " " }}
      scroll={{ x: "max-content" }}
    />
  );
}
