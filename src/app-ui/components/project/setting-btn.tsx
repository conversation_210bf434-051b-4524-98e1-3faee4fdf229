import { Form, Input, Modal, App, Typography } from "antd";
import { PrjNameReg } from "@/lib/domain/project/constants";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { updateProject } from "@/app-ui/actions/safe-actions/project";
import { ProjectsRes } from "@/lib/query/svc";
import { useHandleApiErrorDefault } from "@/lib/utils";

type FieldType = {
  name: string;
  remark?: string;
};

export function PrjBaseItems() {
  return (
    <>
      <Form.Item
        name="name"
        label="名称"
        validateFirst
        rules={[
          {
            required: true,
            message: "请输入项目名称",
          },
          {
            pattern: PrjNameReg,
            message: "不得使用空格，1~30 位",
          },
        ]}
      >
        <Input maxLength={30} placeholder="不得使用空格，1~30 位" />
      </Form.Item>

      <Form.Item name="remark" label="备注">
        <Input.TextArea maxLength={300} placeholder="不超过 300 字" />
      </Form.Item>
    </>
  );
}

export function PrjSettingBtn({ project }: { project: ProjectsRes }) {
  const router = useRouter();
  const [form] = Form.useForm<FieldType>();
  const { message } = App.useApp();

  const handleApiError = useHandleApiErrorDefault();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const initialValues = {
    name: project.name,
    remark: project.remark ?? undefined,
  };

  const { execute: doUpdatePrj, isExecuting } = useAction(updateProject, {
    onSuccess: () => {
      setIsModalOpen(false);
      form.resetFields();
      message.success("项目设置成功");
      router.refresh();
    },
    onError: handleApiError,
  });

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  return (
    <>
      <div onClick={() => setIsModalOpen(true)}>设置</div>
      <Modal
        title="项目设置"
        open={isModalOpen}
        onCancel={handleCancel}
        onOk={form.submit}
        okButtonProps={{ loading: isExecuting }}
        okText="保存"
        cancelText="取消"
        maskClosable={false}
      >
        <Form
          initialValues={initialValues}
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          onFinish={(values) =>
            doUpdatePrj({
              projectId: project.id,
              ...values,
            })
          }
        >
          <Form.Item label="ID">
            <Typography.Text>{project.id}</Typography.Text>
          </Form.Item>
          <PrjBaseItems />
        </Form>
      </Modal>
    </>
  );
}
