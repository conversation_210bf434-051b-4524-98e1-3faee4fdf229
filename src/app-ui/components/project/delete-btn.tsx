import { deleteProject } from "@/app-ui/actions/safe-actions/project";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { App, Form, Input, Modal } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import Text from "antd/es/typography/Text";

type FieldType = {
  projectName: string;
};

function PrjDeleteItems({ prjName }: { prjName: string }) {
  return (
    <Form.Item
      label="确认删除"
      name="confirm"
      rules={[
        {
          required: true,
          message: "请准确输入完整的项目名称",
          pattern: new RegExp(`^${prjName}$`),
        },
      ]}
    >
      <Input placeholder="请准确输入完整的项目名称，点击确认后进入项目删除流程" />
    </Form.Item>
  );
}

export function PrjDeleteBtn({
  projectId,
  projectName,
}: {
  projectId: string;
  projectName: string;
}) {
  const router = useRouter();
  const [form] = Form.useForm<FieldType>();
  const { message, modal } = App.useApp();

  const handleApiError = useHandleApiErrorDefault();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const doubleCheck = () => {
    modal.confirm({
      title: "再次确认删除项目",
      content: (
        <Text>
          请再次确认是否<Text type="danger"> 删除 </Text>项目
          <Text type="danger"> {projectName} </Text>
          ，删除后项目内所有信息不可恢复，谨慎操作。
        </Text>
      ),
      onOk: async () => {
        await deleteProject({ projectId })
          .then(throwIfSafeActionError)
          .catch(handleApiError);
        message.success("项目删除成功");
        Modal.destroyAll();
        router.refresh();
      },
      footer(_, { OkBtn, CancelBtn }) {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      },
    });
  };

  const handleDelete = () => {
    modal.confirm({
      title: "删除项目",
      content: (
        <Text>
          请确认是否<Text type="danger"> 删除 </Text>项目
          <Text type="danger"> {projectName} </Text>
          ，删除后不可恢复。
        </Text>
      ),
      onOk: doubleCheck,
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  return (
    <>
      <div onClick={() => setIsModalOpen(true)}>删除</div>
      <Modal
        title="删除项目"
        open={isModalOpen}
        onOk={form.submit}
        onCancel={handleCancel}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={form}
          onFinish={handleDelete}
        >
          <PrjDeleteItems prjName={projectName} />
        </Form>
      </Modal>
    </>
  );
}
