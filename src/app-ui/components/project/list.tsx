"use client";

import type { ProjectsRes } from "@/lib/query/svc";
import {
  Button,
  Flex,
  Form,
  Modal,
  App,
  type TableProps,
  Dropdown,
} from "antd";
import Table from "@/app-ui/components/ui/table";
import { useState } from "react";
import { createProject } from "@/app-ui/actions/safe-actions/project";
import { useAction } from "next-safe-action/hooks";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  DATE_FORMAT,
  formatDate,
  TIME_FORMAT,
  useHandleApiErrorDefault,
} from "@/lib/utils";
import { AiOutlineEllipsis } from "react-icons/ai";
import {
  PrjBaseItems,
  PrjSettingBtn,
} from "@/app-ui/components/project/setting-btn";
import { PrjDeleteBtn } from "@/app-ui/components/project/delete-btn";
import Text from "antd/es/typography/Text";

type FieldType = {
  name: string;
  remark?: string;
};

function PrjListDateRender({ date }: { date: Date }) {
  return (
    <div className="text-black/45">
      <div>{formatDate(date, DATE_FORMAT)}</div>
      <div>{formatDate(date, TIME_FORMAT)}</div>
    </div>
  );
}

function PrjListAction({ record }: { record: ProjectsRes }) {
  return (
    <Flex gap={8} align="center">
      <Link href={`/project/${record.id}`} target="_blank">
        <Button color="primary" variant="link" size="small">
          查看
        </Button>
      </Link>
      <Dropdown
        menu={{
          items: [
            {
              key: "setting",
              label: <PrjSettingBtn project={record} />,
            },
            {
              key: "delete",
              label: (
                <PrjDeleteBtn projectId={record.id} projectName={record.name} />
              ),
            },
          ],
        }}
      >
        <Button color="primary" variant="text" className="px-2">
          <AiOutlineEllipsis size={16} />
        </Button>
      </Dropdown>
    </Flex>
  );
}

function PrjListTitle() {
  const router = useRouter();
  const [form] = Form.useForm<FieldType>();
  const { message } = App.useApp();

  const handleApiError = useHandleApiErrorDefault();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const { execute: doCreatePrj, isExecuting } = useAction(createProject, {
    onSuccess: () => {
      setIsModalOpen(false);
      form.resetFields();
      message.success("创建项目成功");
      router.refresh();
    },
    onError: handleApiError,
  });

  const onCreatePrjClick = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  return (
    <Flex align="center" justify="space-between" className="my-4 text-sm">
      全部项目
      <Button type="primary" onClick={onCreatePrjClick}>
        创建项目
      </Button>
      <Modal
        title="创建项目"
        open={isModalOpen}
        onCancel={handleCancel}
        onOk={form.submit}
        okButtonProps={{ loading: isExecuting }}
        okText="立即创建"
        cancelText="取消"
        maskClosable={false}
      >
        <Form
          form={form}
          onFinish={doCreatePrj}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <PrjBaseItems />
        </Form>
      </Modal>
    </Flex>
  );
}

export type PrjListProps = {
  projects: ProjectsRes[];
};

export function PrjList({ projects }: PrjListProps) {
  const columns: TableProps<ProjectsRes>["columns"] = [
    {
      title: "项目名称",
      dataIndex: "name",
      render: (_, { name, id }) => {
        return (
          <>
            <div className="text-black/88">{name}</div>
            <div className="text-black/45">ID:{id}</div>
          </>
        );
      },
    },
    {
      width: 125,
      title: "信息",
      render: () => <span className="text-black/45">个人项目</span>,
    },
    {
      width: 125,
      title: "创建时间",
      dataIndex: "created_at",
      render: (created_at: Date) => {
        return <PrjListDateRender date={created_at} />;
      },
    },
    {
      width: 125,
      title: "更新时间",
      dataIndex: "updated_at",
      render: (updated_at: Date) => {
        return <PrjListDateRender date={updated_at} />;
      },
    },
    {
      title: "备注",
      dataIndex: "remark",
      ellipsis: true,
      render: (remark) => (
        <Text className="text-black/45" ellipsis={{ tooltip: remark }}>
          {remark || "-"}
        </Text>
      ),
    },
    {
      title: "操作",
      render: (record: ProjectsRes) => <PrjListAction record={record} />,
    },
  ];

  return (
    <>
      <PrjListTitle />
      <Table rowKey="id" columns={columns} dataSource={projects} />
    </>
  );
}
