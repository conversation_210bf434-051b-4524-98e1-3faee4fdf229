import { Tabs, type TabsProps } from "antd";

export function PrjAppPanel({ projectId }: { projectId: string }) {
  const items: TabsProps["items"] = [
    {
      key: "apps",
      label: <div className="flex-center  gap-1 ">应用</div>,
      children: <>apps- {projectId}</>,
    },
  ];

  return (
    <Tabs
      defaultActiveKey="apps"
      tabPosition="left"
      items={items}
      className="h-full"
    />
  );
}
