import { DiskOperate } from "@/app-ui/components/disk/operate";
import { DiskViewer } from "@/app-ui/components/disk/viewer";
import { ItemOperate } from "@/app-ui/components/item/operate";
import { ItemViewer } from "@/app-ui/components/item/viewer";
import styles from "@/app-ui/components/project/resource.module.css";
import { UploadProgressBar } from "@/app-ui/components/upload/ upload-progress-bar";
import CloudDiskSvg from "@/public/cloud-disk-outlined.svg";
import DataSvg from "@/public/data-outlined.svg";
import { Tabs, type TabsProps } from "antd";
import clsx from "clsx";

function PrjRscTabLabel({ children }: { children: React.ReactNode }) {
  return <div className="flex-center gap-1 ">{children}</div>;
}

export function PrjResource({ projectId }: { projectId: string }) {
  const items: TabsProps["items"] = [
    {
      label: (
        <PrjRscTabLabel>
          <CloudDiskSvg className="size-4" />
          云盘
        </PrjRscTabLabel>
      ),
      key: "disk",
      children: (
        <>
          <DiskOperate projectId={projectId} />
          <DiskViewer projectId={projectId} treeId="viewer" showMenu />
          <UploadProgressBar />
        </>
      ),
    },
    {
      key: "item",
      label: (
        <PrjRscTabLabel>
          <DataSvg className="size-4" />
          数据
        </PrjRscTabLabel>
      ),
      children: (
        <>
          <ItemOperate projectId={projectId} />
          <ItemViewer projectId={projectId} treeId="viewer" showMenu />
        </>
      ),
    },
  ];

  return (
    <Tabs
      defaultActiveKey="disk"
      tabPosition="left"
      items={items}
      className={clsx(styles.rscTabs, "h-full")}
      indicator={{ size: 14 }}
    />
  );
}
