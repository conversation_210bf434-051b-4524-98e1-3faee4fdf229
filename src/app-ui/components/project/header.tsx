import ProjectSvg from "@/public/project-filled.svg";
import Link from "next/link";
import Text from "antd/es/typography/Text";

export async function PrjHeader({ name }: { name: string }) {
  return (
    <div className="bg-[#272D34] w-full px-4">
      <Link
        href="/project"
        className="text-white flex h-12 gap-2 items-center max-w-fit"
      >
        <ProjectSvg className="size-7 shrink-0" />
        <Text
          className="text-[13px] font-semibold text-white"
          ellipsis={{ tooltip: name }}
        >
          项目 / {name}
        </Text>
      </Link>
    </div>
  );
}
