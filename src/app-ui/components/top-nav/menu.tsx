"use client";

import { Menu, type MenuProps } from "antd";
import Link from "next/link";
import { usePathname } from "next/navigation";
import AISvg from "@/public/irriss-ai-filled.svg";
import ProjectSvg from "@/public/project-filled.svg";

type MenuItem = Required<MenuProps>["items"][number];

export default function LeftMenu() {
  const pathname = usePathname();

  const menuItems: MenuItem[] = [
    {
      key: "/project",
      label: <Link href="/project">项目</Link>,
      icon: <ProjectSvg />,
    },
    {
      key: "/llm",
      label: <Link href="/llm">AI</Link>,
      // antd will add ant-menu-item-icon class to the icon internally, svg need receive this class
      icon: <AISvg />,
    },
  ];

  const selectedKeys = ["/llm", "/project"].filter((x) =>
    pathname.startsWith(x)
  );

  return (
    <Menu
      items={menuItems}
      className="border-none w-full"
      selectedKeys={selectedKeys}
    />
  );
}
