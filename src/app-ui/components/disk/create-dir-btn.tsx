import {
  type DiskTreeId,
  getCurrentFolderDiskPath,
  useUpdateTreeData,
} from "@/lib/app-store/disk-slice";

export type DiskCreateFolderProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskCreateDirBtn({
  diskPath,
  projectId,
  treeId,
}: DiskCreateFolderProps) {
  const updateTreeData = useUpdateTreeData();

  const doCreateDir = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys(expandedKeys) {
        return { expandedKeys: [...expandedKeys, diskPath] };
      },
      newNodePrefix: getCurrentFolderDiskPath(diskPath),
    });
  };

  return <div onClick={doCreateDir}>新建文件夹</div>;
}
