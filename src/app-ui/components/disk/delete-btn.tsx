import { App } from "antd";
import { useUpdateTreeData, type DiskTreeId } from "@/lib/app-store/disk-slice";
import { deleteDiskObject } from "@/app-ui/actions/safe-actions/disk";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";

export type DiskDeleteBtnProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskDeleteBtn({
  diskPath,
  projectId,
  treeId,
}: DiskDeleteBtnProps) {
  const { message, modal } = App.useApp();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const handleDelete = async () => {
    await deleteDiskObject({
      projectId: projectId,
      pathPart: diskPath,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("删除成功");
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [getParentFolderDiskPath(diskPath)],
      }),
    });
  };

  const handleConfirm = () => {
    modal.confirm({
      title: "确认删除",
      content: "确认要删除所选的文件吗？",
      onOk: handleDelete,
    });
  };

  return <div onClick={handleConfirm}>删除</div>;
}
