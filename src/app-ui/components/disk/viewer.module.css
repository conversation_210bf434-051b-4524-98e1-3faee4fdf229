.tree {
  background: transparent;

  :global(.ant-tree-node-content-wrapper) {
    display: flex;
    padding-inline: 0;
  }

  :global(.ant-tree-node-content-wrapper) {
    align-items: center;
  }

  :global(.ant-tree-title) {
    flex: 1;
  }

  :global(.ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-inline-end: 0;
  }

  :global(.ant-tree-iconEle) {
    margin-inline-end: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
