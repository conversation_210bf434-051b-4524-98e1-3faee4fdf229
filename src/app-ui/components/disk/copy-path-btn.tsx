import { DISK_ROOT_NAME, DISK_ROOT_PATH } from "@/lib/app-store/disk-slice";
import { App } from "antd";
import Text from "antd/es/typography/Text";
import path from "path";

export type DiskCopyPathBtnProps = {
  diskPath: string;
};

export function DiskCopyPathBtn({ diskPath }: DiskCopyPathBtnProps) {
  const { message } = App.useApp();
  const text = path.normalize(
    path.join(DISK_ROOT_PATH, DISK_ROOT_NAME, diskPath)
  );
  return (
    <Text
      rootClassName="inline-block w-full [&_.ant-typography-copy]:w-full [&_.ant-typography-copy]:text-left"
      copyable={{
        text,
        tooltips: false,
        icon: <div className="text-irs-primary-content">复制路径</div>,
        onCopy: () => {
          message.success("复制成功");
        },
      }}
    />
  );
}
