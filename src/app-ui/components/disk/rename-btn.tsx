import { useAppDispatch } from "@/lib/app-store/store";
import { diskSlice, type DiskTreeId } from "@/lib/app-store/disk-slice";

export type DiskRenameBtnProps = {
  diskPath: string;
  treeId: DiskTreeId;
};

export function DiskRenameBtn({ diskPath, treeId }: DiskRenameBtnProps) {
  const dispatch = useAppDispatch();
  const doRename = () => {
    dispatch(
      diskSlice.actions.setRenamingNodeKey({
        treeId,
        renamingNodeKey: diskPath,
      })
    );
  };
  return <div onClick={doRename}>重命名</div>;
}
