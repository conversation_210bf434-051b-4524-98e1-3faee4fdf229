import { App } from "antd";
import { moveDiskObject } from "@/app-ui/actions/safe-actions/disk";
import {
  DISK_ROOT_PATH,
  getCurrentFolderDiskPath,
  getParentDiskPaths,
  useUpdateTreeData,
  type DiskTreeId,
} from "@/lib/app-store/disk-slice";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { useShowDiskTransfer } from "@/app-ui/components/disk/show-transfer";
import { getParentFolderDiskPath } from "@/lib/domain/disk/svc";

export type DiskMoveBtnProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskMoveBtn({ diskPath, projectId, treeId }: DiskMoveBtnProps) {
  const { message } = App.useApp();
  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const showTransfer = useShowDiskTransfer({
    title: "移动至",
    projectId,
    doSubmit: async ({ destinationNode }) => {
      await doMoveDiskObject(destinationNode?.key ?? DISK_ROOT_PATH);
    },
  });

  const doMoveDiskObject = async (destinationPath: string) => {
    await moveDiskObject({
      projectId,
      sourcePath: diskPath,
      destinationPath: getCurrentFolderDiskPath(destinationPath),
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("移动成功");
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys(expandedKeys) {
        const desExpandedKeys = getParentDiskPaths(destinationPath);
        return {
          expandedKeys: [...desExpandedKeys, ...expandedKeys],
          refreshKeys: [
            getCurrentFolderDiskPath(destinationPath),
            getParentFolderDiskPath(diskPath),
          ],
        };
      },
    });
  };

  return (
    <div
      onClick={() => {
        showTransfer();
      }}
    >
      移动至
    </div>
  );
}
