import { App } from "antd";
import {
  DISK_ROOT_PATH,
  getCurrentFolderDiskPath,
  getParentDiskPaths,
  useUpdateTreeData,
  type DiskTreeId,
} from "@/lib/app-store/disk-slice";
import { copyDiskObject } from "@/app-ui/actions/safe-actions/disk";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { useShowDiskTransfer } from "@/app-ui/components/disk/show-transfer";

export type DiskCopyBtnProps = {
  diskPath: string;
  projectId: string;
  treeId: DiskTreeId;
};

export function DiskCopyBtn({ diskPath, projectId, treeId }: DiskCopyBtnProps) {
  const { message } = App.useApp();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const showTransfer = useShowDiskTransfer({
    title: "复制到",
    projectId,
    doSubmit: async ({ destinationNode }) => {
      await doCopyDiskObject(destinationNode?.key ?? DISK_ROOT_PATH);
    },
  });

  const doCopyDiskObject = async (destinationPath: string) => {
    await copyDiskObject({
      projectId: projectId,
      sourcePath: diskPath,
      destinationPath: getCurrentFolderDiskPath(destinationPath),
      forbidOverwrite: true,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("复制成功");
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys(expandedKeys) {
        const desExpandedKeys = getParentDiskPaths(destinationPath);
        return {
          expandedKeys: [...desExpandedKeys, ...expandedKeys],
          refreshKeys: [getCurrentFolderDiskPath(destinationPath)],
        };
      },
    });
  };

  return (
    <div
      onClick={() => {
        showTransfer();
      }}
    >
      复制到
    </div>
  );
}
