"use client";

import { DiskCreateDirBtn } from "@/app-ui/components/disk/create-dir-btn";
import { Uploader } from "@/app-ui/components/upload/uploader";
import {
  DISK_ROOT_PATH,
  diskSlice,
  useUpdateTreeData,
} from "@/lib/app-store/disk-slice";
import { useAppSelector } from "@/lib/app-store/store";
import { Dropdown, Flex, type MenuProps, App, Tooltip, Input } from "antd";
import { useState } from "react";
import {
  AiOutlinePlus,
  AiOutlineReload,
  AiOutlineSearch,
} from "react-icons/ai";

export function DiskOperate({ projectId }: { projectId: string }) {
  const treeId = "viewer";
  const { message } = App.useApp();
  const updateTreeData = useUpdateTreeData();

  const [uploadAndCreateOpen, setUploadAndCreateOpen] = useState(false);

  const activeNode = useAppSelector((s) =>
    diskSlice.selectors.selectActiveNode(s, treeId)
  );

  const refreshData = async () => {
    await updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: (expandedKeys) => ({
        refreshKeys: [...expandedKeys, DISK_ROOT_PATH],
      }),
    });
    message.success("刷新成功");
  };

  const items: MenuProps["items"] = [
    {
      key: "uploadFile",
      label: (
        <Uploader projectId={projectId} treeId={treeId}>
          上传文件
        </Uploader>
      ),
    },
    {
      key: "uploadFolder",
      label: (
        <Uploader projectId={projectId} treeId={treeId} type="folder">
          上传文件夹
        </Uploader>
      ),
    },
    {
      type: "divider",
    },
    {
      key: "createDir",
      label: (
        <DiskCreateDirBtn
          diskPath={activeNode?.key ?? DISK_ROOT_PATH}
          projectId={projectId}
          treeId={treeId}
        />
      ),
    },
  ];

  return (
    <Flex
      className="mt-2 px-3 text-[#969A9F] mb-3"
      justify="space-between"
      gap={12}
      align="center"
    >
      <Input
        prefix={<AiOutlineSearch className="size-4 text-[#BFBFBF]" />}
        variant="filled"
        className="flex-1"
        size="small"
        placeholder="搜索"
      />

      <Tooltip title="刷新" placement="bottom">
        <AiOutlineReload
          className="size-4 cursor-pointer"
          onClick={refreshData}
        />
      </Tooltip>

      <Dropdown
        menu={{ items }}
        trigger={["click"]}
        onOpenChange={(open) => {
          if (open) {
            setUploadAndCreateOpen(false);
          }
        }}
      >
        <Tooltip
          title="上传/新建"
          placement="bottom"
          open={uploadAndCreateOpen}
          onOpenChange={setUploadAndCreateOpen}
        >
          <AiOutlinePlus className="size-4 cursor-pointer" />
        </Tooltip>
      </Dropdown>
    </Flex>
  );
}
