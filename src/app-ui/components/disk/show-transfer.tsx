import { Form, Input, type FormProps } from "antd";
import { useShowFormModal } from "@/app-ui/components/utils/show-form-modal";
import { DiskViewer } from "@/app-ui/components/disk/viewer";
import { useAppSelector } from "@/lib/app-store/store";
import {
  DISK_ROOT_PATH,
  diskSlice,
  type DiskTreeNode,
} from "@/lib/app-store/disk-slice";
import { useEffect } from "react";

export type FieldsType = {
  destinationNode?: DiskTreeNode;
};

function ControlledDiskItem({
  projectId,
  onChange,
  value,
}: {
  projectId: string;
  onChange?: (node?: DiskTreeNode) => void;
  value?: DiskTreeNode;
}) {
  const treeId = "transfer";
  const activeNode = useAppSelector((s) =>
    diskSlice.selectors.selectActiveNode(s, "transfer")
  );

  useEffect(() => {
    onChange?.(activeNode);
  }, [activeNode, onChange]);

  return (
    <>
      <Input disabled value={value?.key ?? DISK_ROOT_PATH} />
      <div className="bg-[#efebf7] rounded-lg mt-2 p-2">
        <DiskViewer projectId={projectId} treeId={treeId} />
      </div>
    </>
  );
}

function DiskTransferForm({
  formProps,
  projectId,
}: {
  formProps: FormProps<FieldsType>;
  projectId: string;
}) {
  return (
    <Form<FieldsType>
      {...formProps}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
    >
      <Form.Item label="路径" name="destinationNode" validateFirst>
        <ControlledDiskItem projectId={projectId} />
      </Form.Item>
    </Form>
  );
}

export function useShowDiskTransfer({
  title,
  projectId,
  doSubmit,
}: {
  title: string;
  projectId: string;
  doSubmit: (values: FieldsType) => Promise<void>;
}) {
  const showForm = useShowFormModal<FieldsType>({
    modelConfig: {
      title,
    },
    doSubmit,
    formFC: (formProps) => (
      <DiskTransferForm formProps={formProps} projectId={projectId} />
    ),
  });

  return showForm;
}
