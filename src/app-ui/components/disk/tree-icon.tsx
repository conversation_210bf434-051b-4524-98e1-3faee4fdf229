import FolderOutlined from "@/public/tree-folder-outlined.svg";
import path from "path";
import { AiOutlineCode, AiOutlineFile, AiOutlinePicture } from "react-icons/ai";
import CloudDiskSvg from "@/public/cloud-disk-outlined.svg";
import { isRootPath } from "@/lib/app-store/disk-slice";

export function DiskTreeIcon({
  nodeKey,
  isLeaf,
}: {
  nodeKey: string;
  isLeaf?: boolean;
}) {
  const ext = path.extname(nodeKey).toLowerCase();

  if (isRootPath(nodeKey)) {
    return <CloudDiskSvg className="size-4" />;
  } else if (!isLeaf) {
    return <FolderOutlined className="size-4" />;
  } else if (ext === ".png") {
    return <AiOutlinePicture size={16} />;
  } else if (ext === ".ipynb") {
    return <AiOutlineCode size={16} />;
  } else {
    return <AiOutlineFile className="size-4" />;
  }
}
