"use client";

import { Drawer } from "antd";
import { useEffect, useState } from "react";
import { AiOutlineMenu } from "react-icons/ai";
import { Icon } from "@/app-ui/components/ui/icon";

export default function SidebarTrigger({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);

  const closeDrawer = () => {
    setOpen(false);
  };

  const openDrawer = () => {
    setOpen(true);
  };

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        setOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <>
      <Icon
        onClick={openDrawer}
        component={<AiOutlineMenu size={20} />}
        className="md:hidden"
      />
      <Drawer
        open={open}
        placement="left"
        width={250}
        closable={false}
        rootClassName="[&_.ant-drawer-body]:p-0 [&_.ant-drawer-body]:overflow-hidden [&_.ant-drawer-body]:flex [&_.ant-drawer-body]:flex-col"
        destroyOnClose
        onClose={closeDrawer}
      >
        {children}
      </Drawer>
    </>
  );
}
