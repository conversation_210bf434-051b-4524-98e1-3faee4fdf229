import clsx from "clsx";
export type BaseWrapperProps = React.PropsWithChildren<{
  className?: string;
}>;

function Page({ children, className }: BaseWrapperProps) {
  return (
    <div className={clsx("flex flex-col h-screen", className)}>{children}</div>
  );
}

function Header({ children, className }: BaseWrapperProps) {
  return (
    <header
      className={clsx(
        "flex h-12 px-4 items-center justify-between bg-[#272D34]",
        className
      )}
    >
      {children}
    </header>
  );
}

function Main({ children, className }: BaseWrapperProps) {
  return (
    <main className={clsx("flex flex-auto overflow-hidden", className)}>
      {children}
    </main>
  );
}

function Nav({ children, className }: BaseWrapperProps) {
  return (
    <nav
      className={clsx(
        "hidden md:flex shrink-0 w-[188px] p-2 border-r border-[#EFEFEF]",
        className
      )}
    >
      {children}
    </nav>
  );
}

function Content({ children, className }: BaseWrapperProps) {
  return (
    <div className={clsx("flex flex-auto overflow-hidden", className)}>
      {children}
    </div>
  );
}

export { Page, Header, Main, Nav, Content };
