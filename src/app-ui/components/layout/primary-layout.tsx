import Image from "next/image";
import Link from "next/link";
import SidebarTrigger from "@/app-ui/components/layout/sidebar-trigger";
import UserProfileMenu from "@/app-ui/components/layout/user-profile-menu";
import {
  Content,
  Header,
  Main,
  Nav,
  Page,
} from "@/app-ui/components/layout/layout-blocks";

export type PrimaryLayoutProps = React.PropsWithChildren<{
  menus?: React.ReactNode;
}>;

export default function PrimaryLayout({ children, menus }: PrimaryLayoutProps) {
  const logo = (
    <Link href={"/llm"}>
      <Image
        alt="space-logo"
        src="/space-logo.svg"
        width={344}
        height={30}
        priority
      />
    </Link>
  );

  return (
    <Page>
      <Header>
        <div className="hidden md:block">{logo}</div>
        <SidebarTrigger>
          <Header>{logo}</Header>
          <div className="p-2">{menus}</div>
        </SidebarTrigger>
        <UserProfileMenu />
      </Header>
      <Main>
        <Nav>{menus}</Nav>
        <Content>{children}</Content>
      </Main>
    </Page>
  );
}
