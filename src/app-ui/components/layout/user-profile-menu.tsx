import { di } from "@/app-ui/di";
import { Avatar, Dropdown, type MenuProps } from "antd";
import Text from "antd/es/typography/Text";
import Link from "next/link";
import { Icon } from "@/app-ui/components/ui/icon";
import {
  AiOutlineCreditCard,
  AiOutlineMail,
  AiOutlineUser,
} from "react-icons/ai";

export default async function UserProfileMenu() {
  const user = await di.ensureLogin();
  const prisma = await di.getPrisma();
  const userSvc = await di.getUserSvc();
  const profile = await userSvc.profile(prisma, user);

  const onSignOut = async () => {
    "use server";
    const nextAuth = await di.getNextAuth();
    await nextAuth.signOut();
  };
  const items: MenuProps["items"] = [
    {
      key: "account",
      label: <Link href="/account">个人中心</Link>,
    },
    {
      key: "logout",
      label: <a onClick={onSignOut}>退出</a>,
    },
  ];

  return (
    <div className="flex items-center h-full">
      <div className="flex items-center gap-2 mr-[6px]">
        <Icon href="/msg" component={<AiOutlineMail size={24} />} />
        <Icon
          href="/atp/account"
          component={<AiOutlineCreditCard size={24} />}
        />
      </div>
      <div className="bg-white opacity-20 h-6 w-px mx-2" />
      <Dropdown
        menu={{
          items: items,
        }}
      >
        <div className="flex items-center gap-2 ml-3">
          <Avatar
            icon={<AiOutlineUser size={14} />}
            size={24}
            className="bg-white text-black"
          />
          <Text ellipsis className="max-w-22 text-white">
            {profile.name}
          </Text>
        </div>
      </Dropdown>
    </div>
  );
}
