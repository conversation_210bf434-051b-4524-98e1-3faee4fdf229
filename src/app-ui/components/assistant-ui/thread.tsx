import { cn } from "@/lib/utils";
import {
  ActionBarPrimitive,
  BranchPickerPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  ThreadPrimitive,
  useMessage,
  useThread,
} from "@assistant-ui/react";
import {
  ArrowDownIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  PencilIcon,
  RefreshCwIcon,
  SendHorizontalIcon,
} from "lucide-react";
import { useContext, type FC, type ReactEventHandler } from "react";
import Image from "next/image";
import {
  useCreateThreadBeforeSend,
  useRunConfigReload,
} from "@/app-ui/components/assistant-ui/hacked";
import { MarkdownText } from "@/app-ui/components/assistant-ui/markdown-text";
import { TooltipIconButton } from "@/app-ui/components/assistant-ui/tooltip-icon-button";
import { Button } from "@/app-ui/components/ui/button";
import { ChatContext } from "@/app-ui/context/chat-context";
import { Thinking } from "@/app-ui/components/assistant-ui/thinking";

export const Thread: FC<{
  threadMsgsDomRef: React.RefObject<HTMLDivElement | null>;
}> = ({ threadMsgsDomRef }) => {
  return (
    <ThreadPrimitive.Root className="flex-1 bg-background box-border overflow-auto">
      <ThreadPrimitive.Viewport className="flex h-full flex-col items-center overflow-y-auto scroll-smooth bg-inherit px-4 pt-8">
        <ThreadWelcome />

        <ThreadMessages threadMsgsDomRef={threadMsgsDomRef} />

        <ThreadPrimitive.If empty={false}>
          <div className="min-h-8 grow" />
        </ThreadPrimitive.If>

        <div className="sticky bottom-0 mt-3 flex w-full max-w-[var(--thread-max-width)] flex-col items-center justify-end rounded-t-lg bg-inherit pb-4">
          <ThreadScrollToBottom />
          <Composer />
        </div>
      </ThreadPrimitive.Viewport>
    </ThreadPrimitive.Root>
  );
};

const ThreadScrollToBottom: FC = () => {
  return (
    <ThreadPrimitive.ScrollToBottom asChild>
      <TooltipIconButton
        tooltip="滚动到底部"
        variant="outline"
        className="absolute -top-8 rounded-full disabled:invisible"
      >
        <ArrowDownIcon />
      </TooltipIconButton>
    </ThreadPrimitive.ScrollToBottom>
  );
};

const ThreadWelcome: FC = () => {
  return (
    <ThreadPrimitive.Empty>
      <div className="flex w-full max-w-[var(--thread-max-width)] grow flex-col">
        <div className="flex w-full grow flex-col items-center justify-center">
          <Image
            src="/irriss-chat.svg"
            alt="logo-icon-ai"
            width={272}
            height={138}
            priority
          />
          <span className="text-[#E1E1E2] text-[22px] font-medium mt-6 tracking-[0.2em]">
            与求索者同行
          </span>
        </div>
      </div>
    </ThreadPrimitive.Empty>
  );
};

const ThreadMessages: FC<{
  threadMsgsDomRef: React.RefObject<HTMLDivElement | null>;
}> = ({ threadMsgsDomRef }) => {
  return (
    <div
      ref={threadMsgsDomRef}
      className="w-full max-w-[calc(var(--thread-max-width)+3rem)] p-6 flex flex-col items-center"
    >
      <ThreadPrimitive.Messages
        components={{
          UserMessage: UserMessage,
          EditComposer: EditComposer,
          AssistantMessage: AssistantMessage,
        }}
      />
    </div>
  );
};

const Composer: FC = () => {
  const lastMsgId = useThread((x) => x.messages.at(-1))?.id || null;
  const onSend = useCreateThreadBeforeSend({ parentId: lastMsgId });

  return (
    <ComposerPrimitive.Root
      onSubmit={onSend}
      className="focus-within:border-ring/20 flex flex-col w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-xs transition-colors ease-in"
    >
      <ComposerPrimitive.Input
        rows={1}
        autoFocus
        placeholder="向AI发送消息"
        className="placeholder:text-muted-foreground max-h-80 w-full grow resize-none border-none bg-transparent px-2 py-4 text-sm outline-hidden focus:ring-0 disabled:cursor-not-allowed"
      />
      <ComposerAction onSend={onSend} />
    </ComposerPrimitive.Root>
  );
};

const ComposerAction = ({ onSend }: { onSend: ReactEventHandler }) => {
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <ComposerPrimitive.Send asChild>
          <TooltipIconButton
            tooltip="发送"
            variant="default"
            className="my-2.5 size-8 p-2 transition-opacity ease-in bg-irs-primary hover:bg-irs-primary/80 cursor-pointer"
            onClick={onSend}
          >
            <SendHorizontalIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Send>
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <ComposerPrimitive.Cancel asChild>
          <TooltipIconButton
            tooltip="停止"
            variant="default"
            className="my-2.5 size-8 p-2 transition-opacity ease-in bg-irs-primary hover:bg-irs-primary/80 cursor-pointer"
          >
            <CircleStopIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Cancel>
      </ThreadPrimitive.If>
    </>
  );
};

const UserMessage: FC = () => {
  const { user } = useContext(ChatContext);
  return (
    <>
      <div className="text-xs text-[#8C8C8C] text-right w-full max-w-[var(--thread-max-width)]">
        {user.name}
      </div>
      <MessagePrimitive.Root className="grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] pb-4 mt-1">
        <UserActionBar />

        <div className="bg-[#EFEFEF] text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words rounded-[6px] rounded-tr-none px-5 py-2.5 col-start-2 row-start-2">
          <MessagePrimitive.Content />
        </div>

        <BranchPicker className="col-span-full col-start-1 row-start-3 -mr-1 justify-end" />
      </MessagePrimitive.Root>
    </>
  );
};

const UserActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      className="flex flex-col items-end col-start-1 row-start-2 mr-3 mt-2.5"
    >
      <ActionBarPrimitive.Edit asChild>
        <TooltipIconButton tooltip="编辑">
          <PencilIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Edit>
    </ActionBarPrimitive.Root>
  );
};

const EditComposer: FC = () => {
  const parentId = useMessage().parentId;
  const onSend = useCreateThreadBeforeSend({ parentId });

  return (
    <ComposerPrimitive.Root
      onSubmit={onSend}
      className="bg-muted my-4 flex w-full max-w-[var(--thread-max-width)] flex-col gap-2 rounded-xl"
    >
      <ComposerPrimitive.Input className="text-foreground flex h-8 w-full resize-none bg-transparent p-4 pb-0 outline-hidden" />

      <div className="mx-3 mb-3 flex items-center justify-center gap-2 self-end">
        <ComposerPrimitive.Cancel asChild>
          <Button variant="ghost">取消</Button>
        </ComposerPrimitive.Cancel>
        <ComposerPrimitive.Send asChild onClick={onSend}>
          <Button>发送</Button>
        </ComposerPrimitive.Send>
      </div>
    </ComposerPrimitive.Root>
  );
};

const ReasoningComponent = () => {
  return (
    <div className="pl-3 border-l mb-3 opacity-70">
      <MarkdownText />
    </div>
  );
};

const AssistantMessage: FC = () => {
  const running = useMessage((state) => state.status?.type === "running");

  return (
    <MessagePrimitive.Root className="relative w-full max-w-[var(--thread-max-width)] py-4">
      <div className="text-xs text-[#8C8C8C]">AI Model</div>
      <div className="text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words leading-7 col-span-2 col-start-2 row-start-1 my-1">
        <MessagePrimitive.Content
          components={{
            Text: MarkdownText,
            Reasoning: ReasoningComponent,
            Empty: () => (running ? <Thinking /> : null),
          }}
        />
      </div>

      <AssistantActionBar />

      <BranchPicker className="col-start-2 row-start-2 -ml-2 mr-2" />
    </MessagePrimitive.Root>
  );
};

const AssistantActionBar: FC = () => {
  const onReload = useRunConfigReload({});

  return (
    <>
      <div className="row-start-2 min-h-6"></div>
      <ActionBarPrimitive.Root
        hideWhenRunning
        autohide="always"
        autohideFloat="never"
        className="absolute bottom-0 left-0 text-muted-foreground flex gap-1 col-start-3 row-start-2 -ml-1 data-floating:bg-background data-floating:data-floating:rounded-md data-floating:border data-floating:p-1 data-floating:shadow-xs"
      >
        {/* <MessagePrimitive.If speaking={false}>
      <ActionBarPrimitive.Speak asChild>
        <TooltipIconButton tooltip="Read aloud">
          <AudioLinesIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Speak>
    </MessagePrimitive.If>
    <MessagePrimitive.If speaking>
      <ActionBarPrimitive.StopSpeaking asChild>
        <TooltipIconButton tooltip="Stop">
          <StopCircleIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.StopSpeaking>
    </MessagePrimitive.If> */}
        <ActionBarPrimitive.Copy asChild>
          <TooltipIconButton tooltip="复制">
            <MessagePrimitive.If copied>
              <CheckIcon />
            </MessagePrimitive.If>
            <MessagePrimitive.If copied={false}>
              <CopyIcon />
            </MessagePrimitive.If>
          </TooltipIconButton>
        </ActionBarPrimitive.Copy>
        <ActionBarPrimitive.Reload asChild onClick={onReload}>
          <TooltipIconButton tooltip="重新生成">
            <RefreshCwIcon />
          </TooltipIconButton>
        </ActionBarPrimitive.Reload>
      </ActionBarPrimitive.Root>
    </>
  );
};

const BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({
  className,
  ...rest
}) => {
  return (
    <BranchPickerPrimitive.Root
      hideWhenSingleBranch
      className={cn(
        "text-muted-foreground inline-flex items-center text-xs",
        className
      )}
      {...rest}
    >
      <BranchPickerPrimitive.Previous asChild>
        <TooltipIconButton tooltip="上一页">
          <ChevronLeftIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Previous>
      <span className="font-medium">
        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />
      </span>
      <BranchPickerPrimitive.Next asChild>
        <TooltipIconButton tooltip="下一页">
          <ChevronRightIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Next>
    </BranchPickerPrimitive.Root>
  );
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};
