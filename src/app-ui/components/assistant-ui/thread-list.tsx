import {
  ThreadListItemPrimitive,
  ThreadListPrimitive,
  useAssistantRuntime,
  useThreadList,
  useThreadListItem,
  useThreadListItemRuntime,
} from "@assistant-ui/react";
import { useContext, useEffect, useRef, useState, type FC } from "react";
import { Button } from "@/app-ui/components/ui/button";
import { App, Dropdown, Input, type MenuProps } from "antd";
import { useQueryState } from "nuqs";
import { AiOutlineMore, AiOutlinePlus } from "react-icons/ai";
import { ThreadItemContext } from "./thread-item-context";

export const QueryThreadIdName = "thread_id";

export const ThreadList = () => {
  const [queryThreadId] = useQueryState(QueryThreadIdName, {
    history: "push",
  });
  const ar = useAssistantRuntime();
  const tlir = useThreadListItemRuntime();

  const mainThreadId = useThreadList((s) => s.mainThreadId);
  const threadLength = useThreadList((s) => s.threads.length);
  console.log("render => ", mainThreadId, queryThreadId, threadLength);
  const switchedByQueryyString = useRef(false);

  useEffect(
    () => {
      if (threadLength > 0 && !switchedByQueryyString.current) {
        // threads are loaded
        if (queryThreadId && queryThreadId != mainThreadId) {
          console.log("switch to thread by load", queryThreadId);
          ar.threads.switchToThread(queryThreadId);
        } else if (
          !queryThreadId &&
          mainThreadId &&
          !mainThreadId.startsWith("__LOCALID_")
        ) {
          console.log("switch to new by load ");
          ar.threads.switchToNewThread();
        }
        switchedByQueryyString.current = true;
      }
    },
    // this controls when thread are loaded at first refresh
    // only check if threads loaded but ignore mainthreadid changed,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [ar, threadLength]
  );

  useEffect(
    () => {
      if (switchedByQueryyString.current) {
        if (queryThreadId && queryThreadId != mainThreadId) {
          console.log("switch to thread by query change", queryThreadId);
          ar.threads.switchToThread(queryThreadId);
        } else if (!queryThreadId) {
          console.log("switch to new by query change");
          ar.threads.switchToNewThread();
        }
      }
    },
    // this controlls history change by nav
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [ar, queryThreadId]
  );

  useEffect(() => {
    return ar.thread.unstable_on("initialize", () => {
      if (ar.thread.getState().messages.length === 0) {
        const dispose = ar.thread.unstable_on("run-end", () => {
          dispose();
          tlir.generateTitle();
        });
      }
    });
  }, [ar, tlir]);

  return (
    <ThreadListPrimitive.Root className="flex-1 flex flex-col items-stretch gap-1.5 py-3 overflow-hidden border-r border-[#0000000f]">
      <ThreadListNew />
      <div className="overflow-y-auto px-3">
        <ThreadListItems />
      </div>
    </ThreadListPrimitive.Root>
  );
};

const ThreadListNew: FC = () => {
  const isMain = useThreadList((t) => t.newThread === t.mainThreadId);
  const [queryThreadId, setQueryThreadId] = useQueryState(QueryThreadIdName, {
    history: "push",
  });
  const loaded = useRef(false);
  useEffect(
    () => {
      if (loaded.current && isMain && queryThreadId) {
        console.log("change router to new");
        setQueryThreadId(null);
      }

      loaded.current = true;
    },
    // ignore queryThreadId
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isMain, setQueryThreadId]
  );

  return (
    <ThreadListPrimitive.New asChild className="hover:cursor-pointer mx-3">
      <Button
        className="data-[active]:bg-muted hover:bg-muted flex items-center justify-start gap-2 rounded-lg px-2.5 py-2 text-start text-irs-primary hover:text-irs-primary/80"
        variant="ghost"
      >
        <AiOutlinePlus />
        新建对话
      </Button>
    </ThreadListPrimitive.New>
  );
};

const ThreadListItems: FC = () => {
  return <ThreadListPrimitive.Items components={{ ThreadListItem }} />;
};

const ThreadListItem: FC = () => {
  const tli = useThreadListItem();
  const [queryThreadId, setQueryThreadId] = useQueryState(QueryThreadIdName, {
    history: "push",
  });
  const [isEditing, setIsEditing] = useState(false);

  useEffect(
    () => {
      const remoteId = tli.remoteId;
      if (tli.isMain && remoteId && remoteId != queryThreadId) {
        console.log("change router to thread", remoteId);
        setQueryThreadId(remoteId);
      }
    },
    // ignore queryThreadId
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setQueryThreadId, tli.isMain, tli.remoteId]
  );

  const value = { isEditing, changeEditing: setIsEditing };

  return (
    <ThreadItemContext value={value}>
      <ThreadListItemImpl />
    </ThreadItemContext>
  );
};

const ThreadListItemImpl: FC = () => {
  const { isEditing, changeEditing } = useContext(ThreadItemContext);
  const title = useThreadListItem((m) => m.title);
  const { rename } = useThreadListItemRuntime();

  const onBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    rename(e.target.value);
    changeEditing(false);
  };

  const onPressEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    rename((e.target as HTMLInputElement).value);
    changeEditing(false);
  };

  if (isEditing) {
    return (
      <div className="[&_.ant-input-outlined:focus]:shadow-none">
        <Input
          defaultValue={title}
          onBlur={onBlur}
          autoFocus
          onPressEnter={onPressEnter}
        />
      </div>
    );
  }
  return (
    <ThreadListItemPrimitive.Root className="group data-[active]:bg-muted hover:bg-muted focus-visible:bg-muted focus-visible:ring-ring flex items-center gap-2 rounded-lg transition-all focus-visible:outline-none focus-visible:ring-2">
      <ThreadListItemPrimitive.Trigger className="flex-grow text-start hover:cursor-pointer min-w-0">
        <ThreadListItemTitle />
      </ThreadListItemPrimitive.Trigger>
      <ThreadListItemActions />
    </ThreadListItemPrimitive.Root>
  );
};

const ThreadListItemTitle: FC = () => {
  return (
    <p className="text-sm truncate px-3 py-2">
      <ThreadListItemPrimitive.Title fallback="..." />
    </p>
  );
};

const ThreadListItemArchive: FC = () => {
  const { modal } = App.useApp();
  const { archive } = useThreadListItemRuntime();

  const handleArchive = () => {
    modal.confirm({
      title: "确认删除",
      content: "确定删除该对话吗？",
      onOk: () => {
        archive();
      },
    });
  };
  return <div onClick={handleArchive}>删除</div>;
};

const ThreadListItemActions = () => {
  const { changeEditing } = useContext(ThreadItemContext);
  const items: MenuProps["items"] = [
    {
      key: "rename",
      label: "重命名",
      onClick: () => {
        changeEditing(true);
      },
    },
    {
      key: "archive",
      label: <ThreadListItemArchive />,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      trigger={["click"]}
      className="hidden group-hover:block"
    >
      <a onClick={(e) => e.preventDefault()}>
        <AiOutlineMore size={20} className="text-[#8C8C8C]" />
      </a>
    </Dropdown>
  );
};
