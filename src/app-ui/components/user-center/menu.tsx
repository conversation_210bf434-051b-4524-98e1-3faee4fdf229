"use client";

import { Menu, type MenuProps } from "antd";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AiFillSun, AiOutlineMail, AiOutlineUser } from "react-icons/ai";

type MenuItem = Required<MenuProps>["items"][number];

export default function LeftMenu() {
  const pathName = usePathname();

  const menuItems: MenuItem[] = [
    {
      key: "/account",
      label: <Link href="/account">个人中心</Link>,
      icon: <AiOutlineUser size={16} />,
    },
    {
      icon: <AiFillSun />,
      key: "/atp",
      label: <Link href="/atp/account">ATP 账户</Link>,
    },
    {
      key: "/msg",
      label: <Link href="/msg">系统消息</Link>,
      icon: <AiOutlineMail size={16} />,
    },
  ];

  const selectedKeys = ["/account", "/atp", "/msg"].filter((x) =>
    pathName.startsWith(x)
  );

  return (
    <Menu
      items={menuItems}
      className="border-none w-full"
      selectedKeys={selectedKeys}
    />
  );
}
