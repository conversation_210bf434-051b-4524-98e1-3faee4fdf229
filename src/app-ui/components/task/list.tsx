"use client";

import { paths } from "@/app/route-path";
import { formatDate } from "@/lib/utils";
import { Table, TableProps } from "antd";
import Link from "next/link";

export function TaskList({
  projectId,
  tasks,
}: {
  projectId: string;
  tasks: {
    id: string;
    error: string | null;
    task_type: string;
    remote_task_status: string | null;
    start_at: Date;
    finished_at: Date | null;
  }[];
}) {
  tasks = [
    {
      error: null,
      id: "1",
      task_type: "1",
      remote_task_status: "1",
      start_at: new Date(),
      finished_at: new Date(),
    },
    {
      error: "xxxx error",
      id: "2",
      task_type: "2",
      remote_task_status: "2",
      start_at: new Date(),
      finished_at: new Date(),
    },
  ];

  const columns: TableProps["columns"] = [
    {
      title: "id",
      dataIndex: "id",
    },
    {
      title: "type",
      dataIndex: "task_type",
    },
    {
      title: "remote_task_status",
      dataIndex: "remote_task_status",
    },
    {
      title: "start_at",
      dataIndex: "start_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "finished_at",
      dataIndex: "finished_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "error",
      dataIndex: "error",
    },
    {
      title: "操作",
      render(d) {
        return (
          <Link
            href={paths.project.projectId(projectId).task.taskId(d.id).path}
          >
            详情
          </Link>
        );
      },
    },
  ];

  return <Table rowKey={"id"} columns={columns} dataSource={tasks} />;
}
