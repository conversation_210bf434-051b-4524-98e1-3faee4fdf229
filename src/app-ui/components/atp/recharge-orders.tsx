"use client";

import { RechargeOrderStatus } from "@/lib/domain/atp/m";
import type { RechargeOrdersRes } from "@/lib/query/svc";
import { Button, DatePicker, Typography, type TableProps } from "antd";
import dayjs from "dayjs";
import Table from "@/app-ui/components/ui/table";
import { AiOutlineClockCircle } from "react-icons/ai";
import { useQueryState } from "nuqs";
import { RangePickerProps } from "antd/es/date-picker";
import { useState } from "react";
import Big from "big.js";

export function RechargeOrders({
  recharges,
}: {
  recharges: RechargeOrdersRes[];
}) {
  const [startAt, setStartAt] = useQueryState("startAt", {
    history: "push",
  });
  const [endAt, setEndAt] = useQueryState("endAt", {
    history: "push",
  });
  const [, setPage] = useQueryState("page", {
    history: "push",
    defaultValue: "1",
  });

  const [filters, setFilters] = useState({
    startAt: startAt ? dayjs(startAt) : null,
    endAt: endAt ? dayjs(endAt) : null,
  });

  const dataSource = recharges.filter((x) => {
    const statusFilter = x.status !== RechargeOrderStatus.Pending;

    if (startAt && endAt) {
      const start = dayjs(startAt);
      const end = dayjs(endAt);
      const inRange = dayjs(x.created_at).isBetween(start, end, "day", "[]");
      return statusFilter && inRange;
    }

    return statusFilter;
  });

  const onRangeChange: RangePickerProps["onChange"] = (dates) => {
    setFilters({
      startAt: dates?.[0] ?? null,
      endAt: dates?.[1] ?? null,
    });
  };

  const onConfirm = () => {
    setPage("1");
    const { startAt, endAt } = filters;
    setStartAt(startAt ? startAt.format("YYYY-MM-DD") : null);
    setEndAt(endAt ? endAt.format("YYYY-MM-DD") : null);
  };

  const onReset = () => {
    setPage("1");
    setFilters({
      startAt: null,
      endAt: null,
    });
    setStartAt(null);
    setEndAt(null);
  };

  const columns: TableProps<RechargeOrdersRes>["columns"] = [
    {
      title: "内容",
      dataIndex: "atp",
      render(v) {
        return "ATP: " + v;
      },
    },
    {
      title: "订单号",
      dataIndex: "id",
      render(v) {
        return (
          <div className="flex">
            <div className="flex group pr-[22px] hover:pr-0">
              <Typography.Text>{v}</Typography.Text>
              <Typography.Text
                copyable={{ text: v }}
                className="[&_.ant-typography-copy]:text-irs-primary ml-2 hidden group-hover:block"
              />
            </div>
          </div>
        );
      },
    },
    {
      title: "支付金额",
      dataIndex: "amount",
      render(v) {
        return Big(v as number)
          .div(100)
          .toNumber()
          .toLocaleString("zh-CN", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
            style: "currency",
            currency: "CNY",
            currencyDisplay: "symbol",
            useGrouping: true,
          });
      },
    },
    {
      title: "支付流水号",
      dataIndex: "pay_transaction_id",
    },
    {
      title: "类型",
      key: "type",
      render: () => "云服务",
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      render(v) {
        return dayjs(v).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      fixed: "right",
      render(v: RechargeOrderStatus) {
        let status = "异常";
        let color = "#FAAD14";
        switch (v) {
          case RechargeOrderStatus.Pending:
            status = "待支付";
            break;
          case RechargeOrderStatus.PaidFailed:
            status = "失败";
            break;
          case RechargeOrderStatus.PaidSuccess:
          case RechargeOrderStatus.Recharged:
            status = "成功";
            color = "#52C41A";
            break;
        }

        return <span style={{ color }}>{status}</span>;
      },
    },
  ];

  const toInvoice = () => {
    window.open(
      "https://doc.weixin.qq.com/forms/AB8AaAczACoARcAyAZSAKsFHobwjgeSMf?page=1",
      "_blank"
    );
  };

  return (
    <>
      <div className="mb-4 flex justify-between items-center gap-6">
        <DatePicker.RangePicker
          suffixIcon={<AiOutlineClockCircle />}
          onChange={onRangeChange}
          maxDate={dayjs()}
          value={[filters.startAt, filters.endAt]}
        />
        <div className="flex gap-2">
          <Button onClick={onConfirm}>确认</Button>
          <Button onClick={onReset}>重置</Button>
        </div>
      </div>

      <Table<RechargeOrdersRes>
        rowKey={"id"}
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: "max-content" }}
        extra={<Button onClick={toInvoice}>申请发票</Button>}
      />
    </>
  );
}
