import ReCharge from "@/app-ui/components/atp/recharge";
import { SktATPBalance } from "@/app-ui/components/skeletons";
import { di } from "@/app-ui/di";
import ATPImg from "@/public/<EMAIL>";
import Image from "next/image";
import { Suspense } from "react";
import { formatAtpAmount } from "@/lib/utils";

async function UserAPT() {
  const session = await di.ensureLogin();
  const prisma = await di.getPrisma();
  const atpSvc = await di.getATPSvc();
  const userATP = await atpSvc.getATPBalance(prisma, session);

  return (
    <div className="text-3xl">{formatAtpAmount(userATP.atp.toString())}</div>
  );
}

async function UserRecharge() {
  const session = await di.ensureLogin();
  const prisma = await di.getPrisma();
  const userSvc = await di.getUserSvc();
  const idVerifiedPms = userSvc
    .profile(prisma, session)
    .then((x) => !!x.id_card_verified);

  return <ReCharge idVerifiedPms={idVerifiedPms} />;
}

export default async function ATPAccount() {
  return (
    <div className="flex flex-col gap-6 max-w-[532px]">
      <div className="relative">
        <Image alt="" src={ATPImg} className="-z-10" />
        <div className="absolute left-5 bottom-3 sm:left-8 sm:bottom-6 text-white">
          <div className="opacity-50 font-medium">余额</div>
          <Suspense fallback={<SktATPBalance />}>
            <UserAPT />
          </Suspense>
        </div>
      </div>

      <UserRecharge />
    </div>
  );
}
