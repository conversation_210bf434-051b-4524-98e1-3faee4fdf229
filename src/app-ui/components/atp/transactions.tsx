"use client";

import type {
  TransactionsSourceInfo,
  TransactionsSourceType,
} from "@/lib/domain/atp/m";
import type { TransactionsRes } from "@/lib/query/svc";
import Table from "@/app-ui/components/ui/table";
import { Button, DatePicker, Select, type TableProps } from "antd";
import dayjs from "dayjs";
import { useQueryState } from "nuqs";
import { AiOutlineClockCircle } from "react-icons/ai";
import { RangePickerProps } from "antd/es/date-picker";
import { useState } from "react";
import { formatAtpAmount } from "@/lib/utils";

export function Transactions({
  transactions,
}: {
  transactions: TransactionsRes[];
}) {
  const [startAt, setStartAt] = useQueryState("startAt", {
    history: "push",
  });
  const [endAt, setEndAt] = useQueryState("endAt", {
    history: "push",
  });
  const [, setPage] = useQueryState("page", {
    history: "push",
    defaultValue: "1",
  });
  const [sourceType, setSourceType] = useQueryState("sourceType", {
    history: "push",
    defaultValue: "all",
  });

  const [increase, setIncrease] = useQueryState("increase", {
    history: "push",
    defaultValue: "all",
  });

  const [filters, setFilters] = useState({
    startAt: startAt ? dayjs(startAt) : null,
    endAt: endAt ? dayjs(endAt) : null,
    sourceType,
    increase,
  });

  const onRangeChange: RangePickerProps["onChange"] = (dates) => {
    setFilters({
      ...filters,
      startAt: dates?.[0] ?? null,
      endAt: dates?.[1] ?? null,
    });
  };

  const onSourceTypeChange = (v: string) => {
    setFilters({
      ...filters,
      sourceType: v,
    });
  };

  const onIncreaseChange = (v: string) => {
    setFilters({
      ...filters,
      increase: v,
    });
  };

  const onConfirm = () => {
    setPage("1");
    const { startAt, endAt, sourceType, increase } = filters;
    setStartAt(startAt ? startAt.format("YYYY-MM-DD") : null);
    setEndAt(endAt ? endAt.format("YYYY-MM-DD") : null);
    setSourceType(sourceType);
    setIncrease(increase);
  };

  const onReset = () => {
    setPage("1");
    setFilters({
      startAt: null,
      endAt: null,
      sourceType: "all",
      increase: "all",
    });
    setStartAt(null);
    setEndAt(null);
  };

  const generateDataRange = (dateRange: string) => {
    // ['2025-05-26 15:00', '15:59']
    const [start, end] = dateRange.split(" - ");
    const startDate = dayjs(start);
    const endDate = dayjs(start.split(" ")[0] + " " + end);
    return [startDate, endDate];
  };

  const sortFunc = (a: TransactionsRes, b: TransactionsRes) => {
    const aType = a.source_info.type;
    const bType = b.source_info.type;

    if (aType === "recharge" && bType === "recharge") {
      return b.created_at.getTime() - a.created_at.getTime();
    } else if (aType === "token-consumation" && bType === "token-consumation") {
      const [aStartDate] = generateDataRange(a.source_info.info.date_range);
      const [bStartDate] = generateDataRange(b.source_info.info.date_range);
      return aStartDate.isAfter(bStartDate) ? -1 : 1;
    } else if (aType === "recharge" && bType === "token-consumation") {
      // a 创建时间在 b 结束时间之后，那 a 在 b 之前，否则 a 在 b 之后
      const aDate = dayjs(a.created_at);
      const [, bEnd] = generateDataRange(b.source_info.info.date_range);
      return aDate.isAfter(bEnd) ? -1 : 1;
    } else if (aType === "token-consumation" && bType === "recharge") {
      // b 创建时间 在 a 结束时间之后，那 b 在 a 之前，否则 b 在 a 之后
      const bDate = dayjs(b.created_at);
      const [, aEnd] = generateDataRange(a.source_info.info.date_range);
      return bDate.isAfter(aEnd) ? 1 : -1;
    }

    return 0;
  };

  const dataSource = transactions.sort(sortFunc).filter((x) => {
    const isIncrease = x.source_type === "recharge";
    const sourceTypeFilter =
      sourceType === "all" ? true : x.source_type === sourceType;
    const increaseFilter =
      increase === "all"
        ? true
        : increase === "increase"
        ? isIncrease
        : !isIncrease;
    let timeFilter = true;
    if (startAt && endAt) {
      const captureTime =
        x.source_info.type === "recharge"
          ? x.created_at
          : generateDataRange(x.source_info.info.date_range)[0];
      const inRange = dayjs(captureTime).isBetween(startAt, endAt, "day", "[]");
      timeFilter = inRange;
    }
    return sourceTypeFilter && increaseFilter && timeFilter;
  });

  const columns: TableProps<TransactionsRes>["columns"] = [
    {
      title: "账单号",
      dataIndex: "id",
    },
    {
      title: "内容",
      dataIndex: "source_info",
      render(v: TransactionsSourceInfo) {
        let shownInfo = "unknown";
        switch (v.type) {
          case "recharge":
            shownInfo = `[购买 ATP]- ATP: ${formatAtpAmount(v.info.atp)}`;
            break;
          case "token-consumation":
            shownInfo = `[AI Chat]- Tokens: ${v.info.tokens}`;
            break;
        }
        return shownInfo;
      },
    },
    {
      title: "类型",
      dataIndex: "source_type",
      render(v: TransactionsSourceType) {
        let shownType = "unknown";
        switch (v) {
          case "recharge":
            shownType = "充值";
            break;
          case "token-consumation":
            shownType = "计费";
            break;
        }
        return shownType;
      },
    },
    {
      title: "数量",
      dataIndex: "atp",
      align: "right",
      render(v: number, { source_type }) {
        let showType = "unknown";
        let color;
        switch (source_type) {
          case "recharge":
            showType = formatAtpAmount(v, { sign: "+" });
            color = "#52C41A";
            break;
          case "token-consumation":
            showType = formatAtpAmount(v, { sign: "-" });
            color = "#FF4D4F";
            break;
        }
        return <span style={{ color }}>{showType}</span>;
      },
    },
    {
      title: "时间",
      dataIndex: "created_at",
      render(_, { source_info, created_at }) {
        switch (source_info.type) {
          case "recharge":
            return dayjs(created_at).format("YYYY-MM-DD HH:mm:ss");
          case "token-consumation":
            return source_info.info.date_range;
        }
      },
    },
  ];

  return (
    <>
      <div className="mb-4 grid grid-cols-2 gap-4 sm:grid-cols-3 lg:flex lg:gap-0 items-center">
        <DatePicker.RangePicker
          suffixIcon={<AiOutlineClockCircle />}
          onChange={onRangeChange}
          maxDate={dayjs()}
          value={[filters.startAt, filters.endAt]}
          className="col-span-2 sm:col-span-3 md:col-span-2 min-w-80"
        />
        <div className="flex items-center">
          <span className="mx-2 whitespace-nowrap">类型:</span>
          <Select
            className="w-full md:w-30"
            value={filters.sourceType}
            onChange={onSourceTypeChange}
            options={[
              {
                value: "all",
                label: "全部",
              },
              {
                value: "recharge",
                label: "充值",
              },
              {
                value: "token-consumation",
                label: "计费",
              },
            ]}
          />
        </div>
        <div className="flex items-center">
          <span className="mx-2 whitespace-nowrap">增减:</span>
          <Select
            value={filters.increase}
            className="w-full md:w-30"
            onChange={onIncreaseChange}
            options={[
              {
                value: "all",
                label: "全部",
              },
              {
                value: "increase",
                label: "增加",
              },
              {
                value: "decrease",
                label: "减少",
              },
            ]}
          />
        </div>

        <div className="col-span-2 sm:col-span-1 md:col-span-2 md:flex-1 flex justify-end gap-2">
          <Button onClick={onConfirm}>确认</Button>
          <Button onClick={onReset}>重置</Button>
        </div>
      </div>
      <Table<TransactionsRes>
        rowKey={"id"}
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: "max-content" }}
        pagination={{
          responsive: true,
          showSizeChanger: true,
          showQuickJumper: true,
          hideOnSinglePage: true,
        }}
      />
    </>
  );
}
