"use client";

import { Tabs, type TabsProps } from "antd";
import { usePathname, useRouter } from "next/navigation";

export default function ATPTabs({
  tabContent,
}: {
  tabContent: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();

  const tabKey = pathname.split("/").pop();

  const onTabChange = (tabKey: string) => {
    router.push(`/atp/${tabKey}`);
  };

  const items: TabsProps["items"] = [
    {
      key: "account",
      label: "账户总览",
      children: tabContent,
    },
    {
      key: "recharge",
      label: "订单列表",
      children: tabContent,
    },
    {
      key: "transaction",
      label: "账单明细",
      children: tabContent,
    },
  ];

  return (
    <Tabs
      className="[&_.ant-tabs-nav]:mx-4 [&_.ant-tabs-content-holder]:px-4"
      activeKey={tabKey}
      items={items}
      onChange={onTabChange}
    />
  );
}
