"use client";
import { Tree } from "antd";
import {
  itemSlice,
  type ItemTreeId,
  type ItemTreeNode,
  useUpdateTreeData,
} from "@/lib/app-store/item-slice";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/store";
import { useEffect } from "react";
import { DirectoryTreeProps } from "antd/lib/tree";
import { ItemTreeTitle } from "@/app-ui/components/item/tree-title";
import styles from "@/app-ui/components/item/viewer.module.css";
import { AiOutlineDown, AiOutlineRight } from "react-icons/ai";
import { ItemTreeIcon } from "@/app-ui/components/item/tree-icon";
import { type AntdTreeNodeAttribute } from "antd/es/tree";

const { DirectoryTree } = Tree;

export type ItemViewerProps = {
  projectId: string;
  treeId: ItemTreeId;
  showMenu?: boolean;
};

export function ItemViewer({ projectId, treeId, showMenu }: ItemViewerProps) {
  const dispatch = useAppDispatch();

  const expandedKeys = useAppSelector((s) =>
    itemSlice.selectors.selectExpandedKeys(s, treeId)
  );
  const treeData = useAppSelector((s) =>
    itemSlice.selectors.selectTreeData(s, treeId)
  );
  const updateTreeData = useUpdateTreeData();

  useEffect(() => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        expandedKeys: [projectId],
      }),
    });
  }, [updateTreeData, projectId, treeId]);

  const updateExpandedKeys = (keys: string[]) => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({ expandedKeys: keys }),
    });
  };

  const handleSelect: DirectoryTreeProps<ItemTreeNode>["onSelect"] = (
    _,
    { selectedNodes }
  ) => {
    dispatch(
      itemSlice.actions.setActiveNode({ treeId, activeNode: selectedNodes[0] })
    );
  };

  const titleRender = (node: ItemTreeNode) => {
    return (
      <ItemTreeTitle
        node={node}
        projectId={projectId}
        treeId={treeId}
        showMenu={showMenu}
      />
    );
  };

  return (
    <DirectoryTree<ItemTreeNode>
      treeData={treeData}
      expandedKeys={expandedKeys}
      titleRender={titleRender}
      onSelect={handleSelect}
      onExpand={(keys) => updateExpandedKeys(keys as string[])}
      className={styles.tree}
      switcherIcon={({ expanded }) =>
        expanded ? <AiOutlineDown size={12} /> : <AiOutlineRight size={12} />
      }
      icon={(props) => {
        const { pid } = props as unknown as AntdTreeNodeAttribute &
          ItemTreeNode;
        return <ItemTreeIcon isLeaf={props.isLeaf} isRoot={!pid} />;
      }}
    />
  );
}
