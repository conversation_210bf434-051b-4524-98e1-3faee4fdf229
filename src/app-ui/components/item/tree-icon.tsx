import FolderSvg from "@/public/item-folder-outlined.svg";
import MBXSvg from "@/public/item-bmx.svg";
import DataSvg from "@/public/data-outlined.svg";

export function ItemTreeIcon({
  isRoot,
  isLeaf,
}: {
  isRoot?: boolean;
  isLeaf: boolean;
}) {
  if (isRoot) {
    return <DataSvg className="size-4" />;
  } else if (!isLeaf) {
    return <FolderSvg className="size-4" />;
  } else {
    return <MBXSvg className="size-4 text-[#171f2b80]" />;
  }
}
