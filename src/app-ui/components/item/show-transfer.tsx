import { ItemViewer } from "@/app-ui/components/item/viewer";
import { useShowFormModal } from "@/app-ui/components/utils/show-form-modal";
import { itemSlice, type ItemTreeNode } from "@/lib/app-store/item-slice";
import { useAppSelector } from "@/lib/app-store/store";
import { Form, type FormProps } from "antd";
import { useEffect } from "react";

type FieldsType = {
  destinationNode?: ItemTreeNode;
};

function ControlledItemFormItem({
  projectId,
  onChange,
}: {
  projectId: string;
  onChange?: (node?: ItemTreeNode) => void;
}) {
  const treeId = "transfer";
  const activeNode = useAppSelector((s) =>
    itemSlice.selectors.selectActiveNode(s, "transfer")
  );

  useEffect(() => {
    onChange?.(activeNode);
  }, [activeNode, onChange]);

  return (
    <div className="bg-[#efebf7] rounded-lg mt-2 p-2">
      <ItemViewer projectId={projectId} treeId={treeId} />
    </div>
  );
}

function ItemTransferForm({
  formProps,
  projectId,
}: {
  formProps: FormProps<FieldsType>;
  projectId: string;
}) {
  return (
    <Form<FieldsType>
      {...formProps}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
    >
      <Form.Item label="路径" name="destinationNode" validateFirst>
        <ControlledItemFormItem projectId={projectId} />
      </Form.Item>
    </Form>
  );
}

export function useShowItemTransfer({
  title,
  projectId,
  doSubmit,
}: {
  title: string;
  projectId: string;
  doSubmit: (values: FieldsType) => Promise<void>;
}) {
  const showFormModal = useShowFormModal<FieldsType>({
    modelConfig: {
      title,
    },
    doSubmit,
    formFC: (formProps) => (
      <ItemTransferForm formProps={formProps} projectId={projectId} />
    ),
  });

  return showFormModal;
}
