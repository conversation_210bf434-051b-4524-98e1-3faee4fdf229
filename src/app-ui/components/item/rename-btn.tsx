import { useAppDispatch } from "@/lib/app-store/store";
import { itemSlice, type ItemTreeId } from "@/lib/app-store/item-slice";

export type ItemRenameBtnProps = {
  id: string;
  treeId: ItemTreeId;
};

export function ItemRenameBtn({ id, treeId }: ItemRenameBtnProps) {
  const dispatch = useAppDispatch();

  const doRename = () => {
    dispatch(
      itemSlice.actions.setRenamingNodeKey({
        treeId,
        renamingNodeKey: id,
      })
    );
  };

  return <div onClick={doRename}>重命名</div>;
}
