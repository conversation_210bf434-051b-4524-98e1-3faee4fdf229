import { ITEM_ROOT_NAME, ITEM_ROOT_PATH } from "@/lib/app-store/item-slice";
import { App } from "antd";
import Text from "antd/es/typography/Text";
import path from "path";

export function ItemCopyPathBtn({ namePath }: { namePath?: string }) {
  const { message } = App.useApp();
  const text = path.normalize(
    path.join(ITEM_ROOT_PATH, ITEM_ROOT_NAME, namePath ?? ITEM_ROOT_PATH)
  );

  return (
    <Text
      rootClassName="inline-block w-full [&_.ant-typography-copy]:w-full [&_.ant-typography-copy]:text-left"
      copyable={{
        text,
        tooltips: false,
        icon: <div className="text-irs-primary-content">复制路径</div>,
        onCopy: () => {
          message.success("复制成功");
        },
      }}
    />
  );
}
