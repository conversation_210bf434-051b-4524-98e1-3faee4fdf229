import { App } from "antd";
import { ItemTreeId, useUpdateTreeData } from "@/lib/app-store/item-slice";
import { useShowItemTransfer } from "@/app-ui/components/item/show-transfer";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { moveItem } from "@/app-ui/actions/safe-actions/item";

export type ItemMoveBtnProps = {
  projectId: string;
  treeId: ItemTreeId;
  pid: string;
  id: string;
};

export function ItemMoveBtn({ projectId, treeId, pid, id }: ItemMoveBtnProps) {
  const { message } = App.useApp();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const showTransfer = useShowItemTransfer({
    title: "移动至",
    projectId,
    doSubmit: async ({ destinationNode }) => {
      let targetId = undefined;
      let targetExpandedKeys: string[] = [];
      let targetRefreshKey = projectId;

      if (destinationNode) {
        const { pid, isLeaf, pids, key } = destinationNode;
        if (!pid) {
          // target is root
          targetExpandedKeys = [projectId];
        } else {
          targetId = isLeaf ? pid : key;
          targetExpandedKeys = isLeaf ? [pid] : [...(pids ?? []), key];
          targetRefreshKey = isLeaf ? pid : key;
        }
      }

      await moveItem({
        projectId,
        sourceId: id,
        targetId,
      })
        .then(throwIfSafeActionError)
        .catch(handleApiError);
      message.success("移动成功");
      updateTreeData({
        projectId,
        treeId,
        generateUpdateKeys(expandedKeys) {
          return {
            expandedKeys: [...expandedKeys, ...targetExpandedKeys],
            refreshKeys: [pid, targetRefreshKey],
          };
        },
      });
    },
  });

  return <div onClick={() => showTransfer()}>移动到</div>;
}
