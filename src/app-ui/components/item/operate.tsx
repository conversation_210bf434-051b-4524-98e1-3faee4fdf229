"use client";

import { Dropdown, Flex, Input, type MenuProps, Tooltip } from "antd";
import {
  AiOutlinePlus,
  AiOutlineReload,
  AiOutlineSearch,
} from "react-icons/ai";
import { useState } from "react";
import { ItemCreateDirBtn } from "@/app-ui/components/item/create-dir-btn";

export function ItemOperate({ projectId }: { projectId: string }) {
  const treeId = "viewer";
  const [createTipOpen, setCreateTipOpen] = useState(false);

  const items: MenuProps["items"] = [
    {
      key: "createFolder",
      label: <ItemCreateDirBtn projectId={projectId} treeId={treeId} />,
    },
  ];
  return (
    <Flex
      className="mt-2 px-3 text-[#969A9F] mb-3"
      justify="space-between"
      gap={12}
      align="center"
    >
      <Input
        prefix={<AiOutlineSearch className="size-4 text-[#BFBFBF]" />}
        variant="filled"
        className="flex-1"
        size="small"
        placeholder="搜索"
      />

      <Tooltip title="刷新" placement="bottom">
        <AiOutlineReload className="size-4 cursor-pointer" />
      </Tooltip>

      <Dropdown
        menu={{ items }}
        trigger={["click"]}
        onOpenChange={(open) => {
          if (open) {
            setCreateTipOpen(false);
          }
        }}
      >
        <Tooltip
          title="新建"
          placement="bottom"
          open={createTipOpen}
          onOpenChange={setCreateTipOpen}
        >
          <AiOutlinePlus className="size-4 cursor-pointer" />
        </Tooltip>
      </Dropdown>
    </Flex>
  );
}
