import { App, Form, Input } from "antd";
import { ItemTreeId, useUpdateTreeData } from "@/lib/app-store/item-slice";
import {
  ItemFolderNameReg,
  ItemFolderNameMessage,
} from "@/lib/domain/item/constants";
import { throwIfSafeActionError, useHandleApiErrorDefault } from "@/lib/utils";
import { createFolder } from "@/app-ui/actions/safe-actions/item";

type FieldsType = {
  name: string;
};

export type ItemCreateDirInputProps = {
  pid: string;
  projectId: string;
  treeId: ItemTreeId;
};

export function ItemCreateDirInput({
  pid,
  projectId,
  treeId,
}: ItemCreateDirInputProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<FieldsType>();

  const updateTreeData = useUpdateTreeData();
  const handleApiError = useHandleApiErrorDefault();

  const refreshFolder = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys: () => ({
        refreshKeys: [pid],
      }),
    });
  };

  const doSubmit = async ({ name }: FieldsType) => {
    await createFolder({
      name,
      projectId,
      pid: projectId === pid ? undefined : pid,
    })
      .then(throwIfSafeActionError)
      .catch(handleApiError);
    message.success("数据组创建成功");
    refreshFolder();
  };

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();

    if (value === "") {
      refreshFolder();
      return;
    }

    try {
      await form.validateFields();
      form.submit();
    } catch {
      refreshFolder();
    }
  };

  return (
    <Form<FieldsType> onFinish={doSubmit} form={form}>
      <Form.Item
        name="name"
        rules={[
          {
            required: true,
            pattern: ItemFolderNameReg,
            message: ItemFolderNameMessage,
          },
        ]}
        className="mb-0"
      >
        <Input
          autoFocus
          onClick={(e) => e.stopPropagation()}
          onBlur={handleBlur}
          size="small"
        />
      </Form.Item>
    </Form>
  );
}
