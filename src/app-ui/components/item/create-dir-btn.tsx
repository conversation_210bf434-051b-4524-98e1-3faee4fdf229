import { ItemTreeId, useUpdateTreeData } from "@/lib/app-store/item-slice";

export type ItemCreateDirBtnProps = {
  id?: string;
  projectId: string;
  treeId: ItemTreeId;
};

export function ItemCreateDirBtn({
  id,
  projectId,
  treeId,
}: ItemCreateDirBtnProps) {
  const updateTreeData = useUpdateTreeData();

  const doCreateDir = () => {
    updateTreeData({
      projectId,
      treeId,
      generateUpdateKeys(expandedKeys) {
        return { expandedKeys: [...expandedKeys, id || projectId] };
      },
      newNodePid: id || projectId,
    });
  };

  return <div onClick={doCreateDir}>新建数据组</div>;
}
