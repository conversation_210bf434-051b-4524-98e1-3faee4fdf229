import clsx from "clsx";
import Link from "next/link";
import React from "react";

export type IconProps = {
  component: React.ReactNode;
  href?: string;
  target?: string;
  className?: string;
  active?: boolean;
  onClick?: (e: React.MouseEvent) => void;
};

const Icon = (props: IconProps) => {
  const { component, href, target, className, onClick, active } = props;

  const cls = clsx(
    "flex size-9 p-[6px] rounded-md transition cursor-pointer justify-center items-center",
    {
      ["text-[#ffe500ff] bg-[#ffffff0f]"]: active,
      ["text-white hover:bg-[#ffffff0f] hover:text-[#ffe500ff]"]: !active,
    },
    className
  );

  const iconComponent = (
    <div className={cls} onClick={onClick}>
      {component}
    </div>
  );

  if (href) {
    return (
      <Link href={href} target={target} className="flex cursor-pointer">
        {iconComponent}
      </Link>
    );
  }
  return iconComponent;
};

export { Icon };
