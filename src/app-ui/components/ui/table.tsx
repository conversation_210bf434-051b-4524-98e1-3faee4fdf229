import { Pagination, Table, TableProps } from "antd";
import { useState } from "react";
import { useQueryState } from "nuqs";

type CustomTableProps<T> = TableProps<T> & {
  extra?: React.ReactNode;
};

const defaultPageSize = 15;

export default function CustomTable<T>(props: CustomTableProps<T>) {
  const [page, setPage] = useQueryState("page", {
    history: "push",
    defaultValue: "1",
  });

  const total = props.dataSource?.length ?? 0;
  const [pageSize, setPageSize] = useState(defaultPageSize);

  const handleChange = (page: number, size: number) => {
    setPage(page + "");
    setPageSize(size);
  };

  const parsePage = (value: string) => {
    const parsed = parseInt(value);
    return isNaN(parsed) ? 1 : parsed;
  };

  const current = parsePage(page);

  return (
    <div>
      <Table
        {...props}
        pagination={false}
        dataSource={props.dataSource?.slice(
          (current - 1) * pageSize,
          current * pageSize
        )}
      />
      {/* 将分页器部分移出 overflow:hidden 的范围 */}
      <div className="relative">
        <div className="flex items-center justify-between py-4 gap-4">
          <div className="flex-none">{props.extra}</div>
          {total > 0 && (
            <Pagination
              responsive
              className="flex justify-end"
              current={current}
              pageSize={pageSize}
              total={total}
              showSizeChanger={total > defaultPageSize}
              showQuickJumper={total > pageSize}
              onChange={handleChange}
              onShowSizeChange={handleChange}
            />
          )}
        </div>
      </div>
    </div>
  );
}
