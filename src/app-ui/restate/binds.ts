import { di } from "@/app-ui/di";
import {
  DeleteItemReqSchema,
  DeleteItemResSchema,
  ItemSvcName,
  RsItemSvc,
  ProcessAfterTaskDoneReqSchema,
  ProcessAfterTaskDoneResSchema,
  RsTaskSvc,
  TaskSvcName,
  UpdateTaskStatusReqSchema,
  UpdateTaskStatusResSchema,
} from "@/lib/infra/restate/client";
import * as rs from "@restatedev/restate-sdk";
import { serde } from "@restatedev/restate-sdk-zod";

export const itemSvcRs = rs.service<string, RsItemSvc>({
  name: ItemSvcName.name,
  handlers: {
    deleteItems: rs.handlers.handler(
      {
        input: serde.zod(DeleteItemReqSchema),
        output: serde.zod(DeleteItemResSchema),
      },
      async (_ctx: rs.Context, { itemIds }) => {
        const itemSvc = await di.getItemSvc();
        await itemSvc.deleteItems({ itemIds });
      }
    ),
  },
});

export const taskSvcRs = rs.service<string, RsTaskSvc>({
  name: TaskSvcName.name,
  handlers: {
    processAfterTaskDone: rs.createServiceHandler(
      {
        input: serde.zod(ProcessAfterTaskDoneReqSchema),
        output: serde.zod(ProcessAfterTaskDoneResSchema),
      },
      async (_ctx: rs.Context, { taskId }) => {
        const taskSvc = await di.getTaskSvc();
        await taskSvc.processAfterTaskDone({ taskId });
      }
    ),

    updateRemoteTaskStatus: rs.createServiceHandler(
      {
        input: serde.zod(UpdateTaskStatusReqSchema),
        output: serde.zod(UpdateTaskStatusResSchema),
      },
      async (_ctx: rs.Context, { taskId, remoteTaskId }) => {
        const taskSvc = await di.getTaskSvc();
        const { keepPolling } = await taskSvc.updateRemoteTaskStatus({
          taskId,
          remoteTaskId,
        });

        if (keepPolling) {
          const delaySeconds = 15;
          _ctx.serviceSendClient(taskSvcRs).updateRemoteTaskStatus(
            {
              taskId,
              remoteTaskId,
            },
            rs.rpc.sendOpts({ delay: { seconds: delaySeconds } })
          );
        }
      }
    ),
  },
});
