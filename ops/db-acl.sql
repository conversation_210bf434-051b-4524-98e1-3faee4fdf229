CREATE USER irs_staging_owner WITH LOGIN PASSWORD 'xxx';
CREATE ROLE irs_staging_readwrite_role;
CREATE ROLE irs_staging_readonly_role;

ALTER DEFAULT PRIVILEGES FOR ROLE irs_staging_owner GRANT ALL ON TABLES TO irs_staging_readwrite_role;
ALTER DEFAULT PRIVILEGES FOR ROLE irs_staging_owner GRANT ALL ON SEQUENCES TO irs_staging_readwrite_role;
ALTER DEFAULT PRIVILEGES FOR ROLE irs_staging_owner GRANT SELECT ON TABLES TO irs_staging_readonly_role;


CREATE USER irs_staging_readwrite WITH LOGIN PASSWORD 'xxx';
GRANT irs_staging_readwrite_role TO irs_staging_readwrite;

CREATE USER irs_staging_readonly WITH LOGIN PASSWORD 'xxx';
GRANT irs_staging_readonly_role TO irs_staging_readonly;

CREATE SCHEMA irs AUTHORIZATION irs_staging_owner;
GRANT USAGE ON SCHEMA irs TO irs_staging_readwrite_role;
GRANT USAGE ON SCHEMA irs TO irs_staging_readonly_role;


---- prod

CREATE USER irs_prod_owner WITH LOGIN PASSWORD 'xxx';
CREATE ROLE irs_prod_readwrite_role;
CREATE ROLE irs_prod_readonly_role;

ALTER DEFAULT PRIVILEGES FOR ROLE irs_prod_owner GRANT ALL ON TABLES TO irs_prod_readwrite_role;
ALTER DEFAULT PRIVILEGES FOR ROLE irs_prod_owner GRANT ALL ON SEQUENCES TO irs_prod_readwrite_role;
ALTER DEFAULT PRIVILEGES FOR ROLE irs_prod_owner GRANT SELECT ON TABLES TO irs_prod_readonly_role;


CREATE USER irs_prod_readwrite WITH LOGIN PASSWORD 'xxx';
GRANT irs_prod_readwrite_role TO irs_prod_readwrite;

CREATE USER irs_prod_readonly WITH LOGIN PASSWORD 'xxx';
GRANT irs_prod_readonly_role TO irs_prod_readonly;

CREATE SCHEMA irs AUTHORIZATION irs_prod_owner;
GRANT USAGE ON SCHEMA irs TO irs_prod_readwrite_role;
GRANT USAGE ON SCHEMA irs TO irs_prod_readonly_role;