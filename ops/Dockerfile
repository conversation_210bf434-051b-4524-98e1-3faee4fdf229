FROM node:24-alpine AS base

# 1. Install dependencies only when needed
FROM base AS builder
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY prisma/schema.prisma ./prisma/
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc*  ./

RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then npm install --global corepack@latest && corepack enable pnpm && COREPACK_NPM_REGISTRY=https://registry.npmmirror.com pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

COPY . .

ENV NEXT_TELEMETRY_DISABLED=1
# This will do the trick, use the corresponding env file for each environment.
# COPY .env.production.sample .env.production
RUN npm run build


# 3. Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/public ./public

# # Automatically leverage output traces to reduce image size
# # https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma/
COPY --from=builder --chown=nextjs:nodejs /app/.env.staging ./
COPY --from=builder --chown=nextjs:nodejs /app/ops/run.sh ./run.sh

USER nextjs

RUN chmod +x ./run.sh

EXPOSE 3000

ENV PORT=3000 HOSTNAME="0.0.0.0" NEXT_TELEMETRY_DISABLED=1

ENTRYPOINT ["./run.sh"]
