name: release

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      tag:
        description: "deploy tag"
        required: true

env:
  irs_img_repo: ${{ vars.IRS_REGISTRY }}/irriss/irs

jobs:
  ci:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.head_commit.message, 'release') && github.event_name != 'workflow_dispatch' }}
    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_PASSWORD: test
        ports:
          - 5432:5432
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          cache: "pnpm"
      - run: pnpm install
      - run: pnpm lint
      - name: Run database migrations
        run: |
          pnpx dotenv-cli -e .env.test -- pnpm exec prisma migrate deploy

      - run: pnpm test:coverage
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  release:
    needs: [ci]
    if: ${{ always() && needs.ci.result == 'success' || needs.ci.result == 'skipped' }}
    runs-on: ubuntu-latest
    steps:
      - uses: googleapis/release-please-action@v4
        id: release
      - name: show outputs
        env:
          release_info: ${{ toJson(steps.release.outputs) }}
        run: echo .
    outputs:
      release_created: ${{ steps.release.outputs.release_created }}
      tag_name: ${{ steps.release.outputs.tag_name }}

  deploy:
    runs-on: ubuntu-latest
    needs: [release]
    if: ${{ always() && ( needs.release.outputs.release_created == 'true' || github.event_name == 'workflow_dispatch' ) }}
    env:
      tag_version: ${{ needs.release.outputs.tag_name || inputs.tag }}
    steps:
      - uses: actions/checkout@v4
      - name: Log in to registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.IRS_REGISTRY }}
          username: ${{ vars.IRS_REGISTRY_USERNAME }}
          password: ${{ secrets.IRS_REGISTRY_PWD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          file: ./ops/Dockerfile
          tags: "${{ env.irs_img_repo }}:${{ env.tag_version }}"
          push: true
          # cache-from: type=gha,scope=diverse
          # cache-to: type=gha,mode=max,scope=diverse

      - name: Checkout GitOps repository
        uses: actions/checkout@v4
        with:
          repository: luminiris/ops
          ref: main
          path: cd-ops
          token: ${{ secrets.OPS_FOR_CI }}

      - name: Update Helm chart appVersion
        run: |
          cd cd-ops
          git config user.email "<EMAIL>"
          git config user.name "auto-ci-action"
          VALUES_PATH="apps/diverse/values.yaml"
          yq -i '.image.tag = "${{ env.tag_version }}"' $VALUES_PATH
          git commit -am "ci: Update diverse appVersion to ${{ env.tag_version }}"
          git push origin main
